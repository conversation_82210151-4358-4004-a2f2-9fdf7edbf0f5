"""
GPU加速的Biot-Savart定律计算
==========================

提供高性能的涡丝诱导速度计算

基于adevice_complement4.md技术规范实现
"""

import numpy as np
from typing import Optional, Tuple, Union
import warnings

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False


class GPUBiotSavart:
    """
    GPU加速的Biot-Savart定律计算器
    
    支持大规模涡丝系统的并行计算
    """
    
    def __init__(self, backend: str = "auto", device_id: int = 0):
        """
        初始化GPU Biot-Savart计算器
        
        Args:
            backend: 计算后端 ("torch", "cupy", "auto")
            device_id: GPU设备ID
        """
        self.backend = backend
        self.device_id = device_id
        self.device = None
        self.is_available = False
        
        self._initialize_backend()
    
    def _initialize_backend(self):
        """初始化计算后端"""
        if self.backend == "auto":
            if TORCH_AVAILABLE:
                self.backend = "torch"
            elif CUPY_AVAILABLE:
                self.backend = "cupy"
            else:
                self.backend = "cpu"
                warnings.warn("GPU后端不可用，回退到CPU计算")
        
        if self.backend == "torch" and TORCH_AVAILABLE:
            try:
                if torch.cuda.is_available():
                    self.device = torch.device(f"cuda:{self.device_id}")
                    self.is_available = True
                else:
                    self.backend = "cpu"
                    warnings.warn("CUDA不可用，回退到CPU计算")
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"Torch初始化失败: {e}")
        
        elif self.backend == "cupy" and CUPY_AVAILABLE:
            try:
                cp.cuda.Device(self.device_id).use()
                self.is_available = True
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"CuPy初始化失败: {e}")
    
    def compute_biot_savart_batch(self, 
                                vortex_segments: np.ndarray,
                                circulations: np.ndarray,
                                target_points: np.ndarray,
                                core_radius: float = 1e-6) -> np.ndarray:
        """
        批量计算Biot-Savart诱导速度
        
        Args:
            vortex_segments: 涡丝段 [n_segments, 2, 3] (起点和终点)
            circulations: 环量 [n_segments]
            target_points: 目标点 [n_points, 3]
            core_radius: 涡核半径
            
        Returns:
            induced_velocities: 诱导速度 [n_points, 3]
        """
        if not self.is_available:
            return self._compute_cpu_fallback(vortex_segments, circulations, target_points, core_radius)
        
        try:
            if self.backend == "torch":
                return self._compute_torch(vortex_segments, circulations, target_points, core_radius)
            elif self.backend == "cupy":
                return self._compute_cupy(vortex_segments, circulations, target_points, core_radius)
            else:
                return self._compute_cpu_fallback(vortex_segments, circulations, target_points, core_radius)
        except Exception as e:
            warnings.warn(f"GPU计算失败，回退到CPU: {e}")
            return self._compute_cpu_fallback(vortex_segments, circulations, target_points, core_radius)
    
    def _compute_torch(self, vortex_segments, circulations, target_points, core_radius):
        """使用PyTorch进行GPU计算"""
        # 转换为torch张量
        segments = torch.from_numpy(vortex_segments).float().to(self.device)
        gamma = torch.from_numpy(circulations).float().to(self.device)
        points = torch.from_numpy(target_points).float().to(self.device)
        
        n_segments = segments.shape[0]
        n_points = points.shape[0]
        
        # 扩展维度进行批量计算
        # segments: [n_segments, 2, 3] -> [n_points, n_segments, 2, 3]
        segments_expanded = segments.unsqueeze(0).expand(n_points, -1, -1, -1)
        # points: [n_points, 3] -> [n_points, n_segments, 3]
        points_expanded = points.unsqueeze(1).expand(-1, n_segments, -1)
        
        # 计算涡丝向量
        r1 = segments_expanded[:, :, 0, :]  # 起点 [n_points, n_segments, 3]
        r2 = segments_expanded[:, :, 1, :]  # 终点 [n_points, n_segments, 3]
        dl = r2 - r1  # 涡丝向量 [n_points, n_segments, 3]
        
        # 计算位置向量
        r1_to_p = points_expanded - r1  # [n_points, n_segments, 3]
        r2_to_p = points_expanded - r2  # [n_points, n_segments, 3]
        
        # 计算叉积 dl × r1_to_p
        cross_product = torch.cross(dl, r1_to_p, dim=2)  # [n_points, n_segments, 3]
        
        # 计算距离
        r1_dist = torch.norm(r1_to_p, dim=2, keepdim=True)  # [n_points, n_segments, 1]
        r2_dist = torch.norm(r2_to_p, dim=2, keepdim=True)  # [n_points, n_segments, 1]
        
        # 计算投影长度
        dl_norm = torch.norm(dl, dim=2, keepdim=True)  # [n_points, n_segments, 1]
        dl_unit = dl / (dl_norm + 1e-12)
        
        cos_theta1 = torch.sum(r1_to_p * dl_unit, dim=2, keepdim=True) / (r1_dist + 1e-12)
        cos_theta2 = torch.sum(r2_to_p * dl_unit, dim=2, keepdim=True) / (r2_dist + 1e-12)
        
        # Biot-Savart公式
        cross_norm_sq = torch.sum(cross_product**2, dim=2, keepdim=True)
        
        # 涡核修正
        core_correction = cross_norm_sq / (cross_norm_sq + core_radius**4)
        
        # 计算诱导速度
        denominator = 4 * np.pi * (cross_norm_sq + 1e-12)
        factor = (cos_theta1 - cos_theta2) / denominator
        factor = factor * core_correction
        
        # 应用环量
        gamma_expanded = gamma.unsqueeze(0).unsqueeze(2)  # [1, n_segments, 1]
        
        induced_velocity = torch.sum(gamma_expanded * factor * cross_product, dim=1)  # [n_points, 3]
        
        return induced_velocity.cpu().numpy()
    
    def _compute_cupy(self, vortex_segments, circulations, target_points, core_radius):
        """使用CuPy进行GPU计算"""
        # 转换为cupy数组
        segments = cp.asarray(vortex_segments, dtype=cp.float32)
        gamma = cp.asarray(circulations, dtype=cp.float32)
        points = cp.asarray(target_points, dtype=cp.float32)
        
        n_segments = segments.shape[0]
        n_points = points.shape[0]
        
        # 初始化结果数组
        induced_velocity = cp.zeros((n_points, 3), dtype=cp.float32)
        
        # 使用CuPy的核函数进行计算
        for i in range(n_segments):
            r1 = segments[i, 0, :]
            r2 = segments[i, 1, :]
            dl = r2 - r1
            
            r1_to_p = points - r1
            r2_to_p = points - r2
            
            # 叉积
            cross_product = cp.cross(dl, r1_to_p)
            
            # 距离
            r1_dist = cp.linalg.norm(r1_to_p, axis=1)
            r2_dist = cp.linalg.norm(r2_to_p, axis=1)
            
            # 投影
            dl_norm = cp.linalg.norm(dl)
            if dl_norm > 1e-12:
                dl_unit = dl / dl_norm
                cos_theta1 = cp.dot(r1_to_p, dl_unit) / (r1_dist + 1e-12)
                cos_theta2 = cp.dot(r2_to_p, dl_unit) / (r2_dist + 1e-12)
                
                # Biot-Savart公式
                cross_norm_sq = cp.sum(cross_product**2, axis=1)
                core_correction = cross_norm_sq / (cross_norm_sq + core_radius**4)
                
                denominator = 4 * np.pi * (cross_norm_sq + 1e-12)
                factor = gamma[i] * (cos_theta1 - cos_theta2) / denominator
                factor = factor * core_correction
                
                induced_velocity += factor[:, cp.newaxis] * cross_product
        
        return cp.asnumpy(induced_velocity)
    
    def _compute_cpu_fallback(self, vortex_segments, circulations, target_points, core_radius):
        """CPU回退计算"""
        n_segments = vortex_segments.shape[0]
        n_points = target_points.shape[0]
        
        induced_velocity = np.zeros((n_points, 3))
        
        for i in range(n_segments):
            r1 = vortex_segments[i, 0, :]
            r2 = vortex_segments[i, 1, :]
            dl = r2 - r1
            gamma = circulations[i]
            
            for j in range(n_points):
                point = target_points[j, :]
                
                r1_to_p = point - r1
                r2_to_p = point - r2
                
                # 叉积
                cross_product = np.cross(dl, r1_to_p)
                cross_norm_sq = np.sum(cross_product**2)
                
                if cross_norm_sq > 1e-12:
                    # 距离
                    r1_dist = np.linalg.norm(r1_to_p)
                    r2_dist = np.linalg.norm(r2_to_p)
                    
                    # 投影
                    dl_norm = np.linalg.norm(dl)
                    if dl_norm > 1e-12:
                        dl_unit = dl / dl_norm
                        cos_theta1 = np.dot(r1_to_p, dl_unit) / (r1_dist + 1e-12)
                        cos_theta2 = np.dot(r2_to_p, dl_unit) / (r2_dist + 1e-12)
                        
                        # 涡核修正
                        core_correction = cross_norm_sq / (cross_norm_sq + core_radius**4)
                        
                        # Biot-Savart公式
                        factor = gamma * (cos_theta1 - cos_theta2) / (4 * np.pi * cross_norm_sq)
                        factor *= core_correction
                        
                        induced_velocity[j, :] += factor * cross_product
        
        return induced_velocity
    
    def get_memory_usage(self) -> dict:
        """获取GPU内存使用情况"""
        if not self.is_available:
            return {"gpu_memory_used": 0, "gpu_memory_total": 0}
        
        try:
            if self.backend == "torch":
                return {
                    "gpu_memory_used": torch.cuda.memory_allocated(self.device),
                    "gpu_memory_total": torch.cuda.get_device_properties(self.device).total_memory
                }
            elif self.backend == "cupy":
                mempool = cp.get_default_memory_pool()
                return {
                    "gpu_memory_used": mempool.used_bytes(),
                    "gpu_memory_total": mempool.total_bytes()
                }
        except Exception:
            pass
        
        return {"gpu_memory_used": 0, "gpu_memory_total": 0}


def create_gpu_biot_savart(backend: str = "auto", device_id: int = 0) -> GPUBiotSavart:
    """
    创建GPU Biot-Savart计算器的工厂函数

    Args:
        backend: 计算后端
        device_id: GPU设备ID

    Returns:
        GPUBiotSavart实例
    """
    return GPUBiotSavart(backend=backend, device_id=device_id)
