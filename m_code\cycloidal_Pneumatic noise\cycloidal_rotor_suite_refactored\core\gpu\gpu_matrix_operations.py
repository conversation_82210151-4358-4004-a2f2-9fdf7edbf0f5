"""
GPU加速的矩阵运算
================

提供高性能的大规模矩阵计算

基于adevice_complement4.md技术规范实现
"""

import numpy as np
from typing import Optional, Tuple, Union, List
import warnings

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False


class GPUMatrixOperations:
    """
    GPU加速的矩阵运算器
    
    支持大规模线性代数运算的GPU加速
    """
    
    def __init__(self, backend: str = "auto", device_id: int = 0):
        """
        初始化GPU矩阵运算器
        
        Args:
            backend: 计算后端 ("torch", "cupy", "auto")
            device_id: GPU设备ID
        """
        self.backend = backend
        self.device_id = device_id
        self.device = None
        self.is_available = False
        
        self._initialize_backend()
    
    def _initialize_backend(self):
        """初始化计算后端"""
        if self.backend == "auto":
            if TORCH_AVAILABLE:
                self.backend = "torch"
            elif CUPY_AVAILABLE:
                self.backend = "cupy"
            else:
                self.backend = "cpu"
                warnings.warn("GPU后端不可用，回退到CPU计算")
        
        if self.backend == "torch" and TORCH_AVAILABLE:
            try:
                if torch.cuda.is_available():
                    self.device = torch.device(f"cuda:{self.device_id}")
                    self.is_available = True
                else:
                    self.backend = "cpu"
                    warnings.warn("CUDA不可用，回退到CPU计算")
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"Torch初始化失败: {e}")
        
        elif self.backend == "cupy" and CUPY_AVAILABLE:
            try:
                cp.cuda.Device(self.device_id).use()
                self.is_available = True
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"CuPy初始化失败: {e}")
    
    def solve_linear_system(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解线性方程组 Ax = b
        
        Args:
            A: 系数矩阵 [n, n]
            b: 右端向量 [n] 或 [n, m]
            
        Returns:
            x: 解向量 [n] 或 [n, m]
        """
        if not self.is_available:
            return self._solve_cpu_fallback(A, b)
        
        try:
            if self.backend == "torch":
                return self._solve_torch(A, b)
            elif self.backend == "cupy":
                return self._solve_cupy(A, b)
            else:
                return self._solve_cpu_fallback(A, b)
        except Exception as e:
            warnings.warn(f"GPU线性求解失败，回退到CPU: {e}")
            return self._solve_cpu_fallback(A, b)
    
    def _solve_torch(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """使用PyTorch求解线性系统"""
        A_tensor = torch.from_numpy(A).float().to(self.device)
        b_tensor = torch.from_numpy(b).float().to(self.device)
        
        # 使用LU分解求解
        try:
            x_tensor = torch.linalg.solve(A_tensor, b_tensor)
            return x_tensor.cpu().numpy()
        except Exception:
            # 如果LU分解失败，使用最小二乘法
            x_tensor = torch.linalg.lstsq(A_tensor, b_tensor).solution
            return x_tensor.cpu().numpy()
    
    def _solve_cupy(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """使用CuPy求解线性系统"""
        A_cp = cp.asarray(A, dtype=cp.float32)
        b_cp = cp.asarray(b, dtype=cp.float32)
        
        try:
            x_cp = cp.linalg.solve(A_cp, b_cp)
            return cp.asnumpy(x_cp)
        except Exception:
            # 如果直接求解失败，使用最小二乘法
            x_cp = cp.linalg.lstsq(A_cp, b_cp)[0]
            return cp.asnumpy(x_cp)
    
    def _solve_cpu_fallback(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """CPU回退求解"""
        try:
            return np.linalg.solve(A, b)
        except Exception:
            return np.linalg.lstsq(A, b, rcond=None)[0]
    
    def matrix_multiply_batch(self, A_batch: np.ndarray, B_batch: np.ndarray) -> np.ndarray:
        """
        批量矩阵乘法
        
        Args:
            A_batch: 矩阵批次 [batch_size, m, k]
            B_batch: 矩阵批次 [batch_size, k, n]
            
        Returns:
            C_batch: 结果矩阵批次 [batch_size, m, n]
        """
        if not self.is_available:
            return self._matmul_cpu_fallback(A_batch, B_batch)
        
        try:
            if self.backend == "torch":
                return self._matmul_torch(A_batch, B_batch)
            elif self.backend == "cupy":
                return self._matmul_cupy(A_batch, B_batch)
            else:
                return self._matmul_cpu_fallback(A_batch, B_batch)
        except Exception as e:
            warnings.warn(f"GPU批量矩阵乘法失败，回退到CPU: {e}")
            return self._matmul_cpu_fallback(A_batch, B_batch)
    
    def _matmul_torch(self, A_batch: np.ndarray, B_batch: np.ndarray) -> np.ndarray:
        """使用PyTorch进行批量矩阵乘法"""
        A_tensor = torch.from_numpy(A_batch).float().to(self.device)
        B_tensor = torch.from_numpy(B_batch).float().to(self.device)
        
        C_tensor = torch.bmm(A_tensor, B_tensor)
        return C_tensor.cpu().numpy()
    
    def _matmul_cupy(self, A_batch: np.ndarray, B_batch: np.ndarray) -> np.ndarray:
        """使用CuPy进行批量矩阵乘法"""
        A_cp = cp.asarray(A_batch, dtype=cp.float32)
        B_cp = cp.asarray(B_batch, dtype=cp.float32)
        
        C_cp = cp.matmul(A_cp, B_cp)
        return cp.asnumpy(C_cp)
    
    def _matmul_cpu_fallback(self, A_batch: np.ndarray, B_batch: np.ndarray) -> np.ndarray:
        """CPU回退批量矩阵乘法"""
        return np.matmul(A_batch, B_batch)
    
    def compute_eigenvalues(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算矩阵特征值和特征向量
        
        Args:
            A: 输入矩阵 [n, n]
            
        Returns:
            eigenvalues: 特征值 [n]
            eigenvectors: 特征向量 [n, n]
        """
        if not self.is_available:
            return self._eigen_cpu_fallback(A)
        
        try:
            if self.backend == "torch":
                return self._eigen_torch(A)
            elif self.backend == "cupy":
                return self._eigen_cupy(A)
            else:
                return self._eigen_cpu_fallback(A)
        except Exception as e:
            warnings.warn(f"GPU特征值计算失败，回退到CPU: {e}")
            return self._eigen_cpu_fallback(A)
    
    def _eigen_torch(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """使用PyTorch计算特征值"""
        A_tensor = torch.from_numpy(A).float().to(self.device)
        eigenvalues, eigenvectors = torch.linalg.eig(A_tensor)
        
        return eigenvalues.cpu().numpy(), eigenvectors.cpu().numpy()
    
    def _eigen_cupy(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """使用CuPy计算特征值"""
        A_cp = cp.asarray(A, dtype=cp.float32)
        eigenvalues, eigenvectors = cp.linalg.eig(A_cp)
        
        return cp.asnumpy(eigenvalues), cp.asnumpy(eigenvectors)
    
    def _eigen_cpu_fallback(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """CPU回退特征值计算"""
        return np.linalg.eig(A)
    
    def compute_svd(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算奇异值分解
        
        Args:
            A: 输入矩阵 [m, n]
            
        Returns:
            U: 左奇异向量 [m, m]
            S: 奇异值 [min(m,n)]
            Vh: 右奇异向量的转置 [n, n]
        """
        if not self.is_available:
            return self._svd_cpu_fallback(A)
        
        try:
            if self.backend == "torch":
                return self._svd_torch(A)
            elif self.backend == "cupy":
                return self._svd_cupy(A)
            else:
                return self._svd_cpu_fallback(A)
        except Exception as e:
            warnings.warn(f"GPU SVD计算失败，回退到CPU: {e}")
            return self._svd_cpu_fallback(A)
    
    def _svd_torch(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """使用PyTorch计算SVD"""
        A_tensor = torch.from_numpy(A).float().to(self.device)
        U, S, Vh = torch.linalg.svd(A_tensor, full_matrices=True)
        
        return U.cpu().numpy(), S.cpu().numpy(), Vh.cpu().numpy()
    
    def _svd_cupy(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """使用CuPy计算SVD"""
        A_cp = cp.asarray(A, dtype=cp.float32)
        U, S, Vh = cp.linalg.svd(A_cp, full_matrices=True)
        
        return cp.asnumpy(U), cp.asnumpy(S), cp.asnumpy(Vh)
    
    def _svd_cpu_fallback(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """CPU回退SVD计算"""
        return np.linalg.svd(A, full_matrices=True)
    
    def compute_matrix_inverse(self, A: np.ndarray) -> np.ndarray:
        """
        计算矩阵逆
        
        Args:
            A: 输入矩阵 [n, n]
            
        Returns:
            A_inv: 逆矩阵 [n, n]
        """
        if not self.is_available:
            return self._inv_cpu_fallback(A)
        
        try:
            if self.backend == "torch":
                return self._inv_torch(A)
            elif self.backend == "cupy":
                return self._inv_cupy(A)
            else:
                return self._inv_cpu_fallback(A)
        except Exception as e:
            warnings.warn(f"GPU矩阵求逆失败，回退到CPU: {e}")
            return self._inv_cpu_fallback(A)
    
    def _inv_torch(self, A: np.ndarray) -> np.ndarray:
        """使用PyTorch计算矩阵逆"""
        A_tensor = torch.from_numpy(A).float().to(self.device)
        A_inv_tensor = torch.linalg.inv(A_tensor)
        
        return A_inv_tensor.cpu().numpy()
    
    def _inv_cupy(self, A: np.ndarray) -> np.ndarray:
        """使用CuPy计算矩阵逆"""
        A_cp = cp.asarray(A, dtype=cp.float32)
        A_inv_cp = cp.linalg.inv(A_cp)
        
        return cp.asnumpy(A_inv_cp)
    
    def _inv_cpu_fallback(self, A: np.ndarray) -> np.ndarray:
        """CPU回退矩阵求逆"""
        return np.linalg.inv(A)


def create_gpu_matrix_operations(backend: str = "auto", device_id: int = 0) -> GPUMatrixOperations:
    """
    创建GPU矩阵运算器的工厂函数
    
    Args:
        backend: 计算后端
        device_id: GPU设备ID
        
    Returns:
        GPUMatrixOperations实例
    """
    return GPUMatrixOperations(backend=backend, device_id=device_id)
