"""
耦合管理器
=========

管理多个求解器之间的耦合计算和数据流
"""

import numpy as np
import time
from typing import Dict, Any, List, Optional, Tuple
from ..interfaces.solver_interface import SolverInterface, SolverConfig, SolverType
from ..interfaces.coupling_interface import CouplingConfig
from ..interfaces.data_interface import DataExchangeInterface, StandardDataExchange
from .aero_acoustic_coupling import AeroAcousticCoupling

class CouplingManager:
    """
    耦合管理器
    
    协调多个求解器的耦合计算，管理数据流和时间步进
    """
    
    def __init__(self, coupling_config: Dict[str, Any]):
        """
        初始化耦合管理器
        
        Args:
            coupling_config: 耦合配置字典
                - coupling_method: 耦合方法
                - time_step: 时间步长
                - max_time: 最大时间
                - output_interval: 输出间隔
        """
        self.config = coupling_config
        
        # 求解器注册表
        self.solvers: Dict[str, SolverInterface] = {}
        self.solver_types: Dict[str, SolverType] = {}
        
        # 耦合求解器
        self.coupling_solvers: Dict[str, AeroAcousticCoupling] = {}
        
        # 数据交换接口
        self.data_exchange = StandardDataExchange()
        
        # 时间控制
        self.current_time = 0.0
        self.time_step = coupling_config.get('time_step', 0.01)
        self.max_time = coupling_config.get('max_time', 1.0)
        self.output_interval = coupling_config.get('output_interval', 0.1)
        
        # 结果存储
        self.results_history = []
        self.coupling_statistics = {}
        
        print(f"✅ 耦合管理器初始化完成")
        print(f"   时间步长: {self.time_step:.4f} s")
        print(f"   最大时间: {self.max_time:.2f} s")
    
    def register_solver(self, name: str, solver: SolverInterface) -> None:
        """
        注册求解器
        
        Args:
            name: 求解器名称
            solver: 求解器实例
        """
        self.solvers[name] = solver
        self.solver_types[name] = solver.solver_type
        
        print(f"注册求解器: {name} ({solver.solver_type.value})")
    
    def create_aero_acoustic_coupling(self, aero_solver_name: str, 
                                    acoustic_solver_name: str,
                                    coupling_config: Optional[CouplingConfig] = None) -> str:
        """
        创建气动-声学耦合
        
        Args:
            aero_solver_name: 气动求解器名称
            acoustic_solver_name: 声学求解器名称
            coupling_config: 耦合配置
            
        Returns:
            coupling_name: 耦合求解器名称
        """
        if aero_solver_name not in self.solvers:
            raise ValueError(f"未找到气动求解器: {aero_solver_name}")
        if acoustic_solver_name not in self.solvers:
            raise ValueError(f"未找到声学求解器: {acoustic_solver_name}")
        
        aero_solver = self.solvers[aero_solver_name]
        acoustic_solver = self.solvers[acoustic_solver_name]
        
        # 验证求解器类型
        if aero_solver.solver_type != SolverType.AERODYNAMIC:
            raise ValueError(f"求解器 {aero_solver_name} 不是气动求解器")
        if acoustic_solver.solver_type != SolverType.ACOUSTIC:
            raise ValueError(f"求解器 {acoustic_solver_name} 不是声学求解器")
        
        # 创建默认耦合配置
        if coupling_config is None:
            coupling_config = CouplingConfig(
                coupling_method=self.config.get('coupling_method', 'one_way'),
                time_synchronization=True,
                spatial_interpolation=True,
                convergence_tolerance=1e-6,
                max_coupling_iterations=10
            )
        
        # 创建耦合求解器
        coupling_name = f"{aero_solver_name}_{acoustic_solver_name}_coupling"
        coupling_solver = AeroAcousticCoupling(
            coupling_config, aero_solver, acoustic_solver, self.data_exchange
        )
        
        self.coupling_solvers[coupling_name] = coupling_solver
        
        print(f"创建气动-声学耦合: {coupling_name}")
        return coupling_name
    
    def run_coupled_simulation(self, geometry_data: Dict[str, Any],
                             boundary_conditions: Dict[str, Any],
                             coupling_name: Optional[str] = None) -> Dict[str, Any]:
        """
        运行耦合仿真
        
        Args:
            geometry_data: 几何数据
            boundary_conditions: 边界条件
            coupling_name: 耦合求解器名称
            
        Returns:
            simulation_results: 仿真结果
        """
        print("开始耦合仿真...")
        start_time = time.time()
        
        # 初始化所有求解器
        self._initialize_solvers(geometry_data)
        
        # 选择耦合求解器
        if coupling_name is None:
            if len(self.coupling_solvers) == 1:
                coupling_name = list(self.coupling_solvers.keys())[0]
            else:
                raise ValueError("必须指定耦合求解器名称")
        
        if coupling_name not in self.coupling_solvers:
            raise ValueError(f"未找到耦合求解器: {coupling_name}")
        
        coupling_solver = self.coupling_solvers[coupling_name]
        
        # 时间步进循环
        time_steps = int(self.max_time / self.time_step)
        output_steps = int(self.output_interval / self.time_step)
        
        for step in range(time_steps):
            self.current_time = step * self.time_step
            
            # 更新边界条件
            current_bc = self._update_boundary_conditions(boundary_conditions, self.current_time)
            
            # 求解耦合时间步
            step_result = coupling_solver.solve_coupled_timestep(self.current_time, current_bc)
            
            # 存储结果
            if step % output_steps == 0:
                self.results_history.append({
                    'time': self.current_time,
                    'step': step,
                    'result': step_result
                })
                
                print(f"时间步 {step:4d}, 时间 {self.current_time:.4f} s, "
                      f"耦合迭代 {step_result['coupling_info']['iterations']:2d}")
        
        # 完成仿真
        computation_time = time.time() - start_time
        
        # 收集统计信息
        self.coupling_statistics = coupling_solver.get_coupling_statistics()
        self.coupling_statistics['total_computation_time'] = computation_time
        
        print(f"耦合仿真完成，总计算时间: {computation_time:.2f} s")
        
        return {
            'results_history': self.results_history,
            'coupling_statistics': self.coupling_statistics,
            'final_time': self.current_time,
            'total_steps': time_steps
        }
    
    def _initialize_solvers(self, geometry_data: Dict[str, Any]) -> None:
        """初始化所有求解器"""
        for name, solver in self.solvers.items():
            if not solver.is_initialized:
                solver.initialize(geometry_data)
                print(f"初始化求解器: {name}")
    
    def _update_boundary_conditions(self, base_bc: Dict[str, Any], 
                                  current_time: float) -> Dict[str, Any]:
        """更新边界条件"""
        updated_bc = base_bc.copy()
        updated_bc['current_time'] = current_time
        
        # 可以在这里添加时间相关的边界条件更新
        # 例如：变化的转速、来流条件等
        
        return updated_bc
    
    def run_sequential_simulation(self, geometry_data: Dict[str, Any],
                                boundary_conditions: Dict[str, Any],
                                solver_sequence: List[str]) -> Dict[str, Any]:
        """
        运行顺序仿真（非耦合）
        
        Args:
            geometry_data: 几何数据
            boundary_conditions: 边界条件
            solver_sequence: 求解器执行顺序
            
        Returns:
            simulation_results: 仿真结果
        """
        print("开始顺序仿真...")
        
        # 初始化求解器
        self._initialize_solvers(geometry_data)
        
        # 验证求解器序列
        for solver_name in solver_sequence:
            if solver_name not in self.solvers:
                raise ValueError(f"未找到求解器: {solver_name}")
        
        # 时间步进循环
        time_steps = int(self.max_time / self.time_step)
        sequential_results = []
        
        for step in range(time_steps):
            self.current_time = step * self.time_step
            current_bc = self._update_boundary_conditions(boundary_conditions, self.current_time)
            
            step_results = {}
            
            # 按顺序执行求解器
            for solver_name in solver_sequence:
                solver = self.solvers[solver_name]
                result = solver.solve_timestep(self.current_time, current_bc)
                step_results[solver_name] = result
                
                # 更新边界条件（传递数据到下一个求解器）
                if solver.solver_type == SolverType.AERODYNAMIC:
                    current_bc['aerodynamic_result'] = result
            
            sequential_results.append({
                'time': self.current_time,
                'step': step,
                'results': step_results
            })
        
        return {
            'sequential_results': sequential_results,
            'solver_sequence': solver_sequence,
            'final_time': self.current_time
        }
    
    def get_solver_info(self) -> Dict[str, Any]:
        """获取求解器信息"""
        solver_info = {}
        
        for name, solver in self.solvers.items():
            solver_info[name] = solver.get_solver_info()
        
        return {
            'registered_solvers': solver_info,
            'coupling_solvers': list(self.coupling_solvers.keys()),
            'current_time': self.current_time,
            'time_step': self.time_step
        }
    
    def export_results(self, filename: str, format: str = 'npz') -> None:
        """
        导出结果
        
        Args:
            filename: 文件名
            format: 文件格式 ('npz', 'json')
        """
        if format == 'npz':
            np.savez(filename, 
                    results_history=self.results_history,
                    coupling_statistics=self.coupling_statistics,
                    config=self.config)
        elif format == 'json':
            import json
            export_data = {
                'coupling_statistics': self.coupling_statistics,
                'config': self.config,
                'solver_info': self.get_solver_info()
            }
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        print(f"结果已导出到: {filename}")
    
    def reset(self) -> None:
        """重置管理器状态"""
        self.current_time = 0.0
        self.results_history.clear()
        self.coupling_statistics.clear()
        
        # 重置所有耦合求解器
        for coupling_solver in self.coupling_solvers.values():
            coupling_solver.reset_coupling_history()
        
        print("耦合管理器状态已重置")


def create_coupling_manager(config: Dict[str, Any]) -> CouplingManager:
    """创建耦合管理器"""
    return CouplingManager(config)


# ==================== 增强的耦合管理器（基于adevice_complement4.md规范） ====================

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.step_times = []
        self.time_steps = []
        self.memory_usage = []

    def record_step(self, step_time: float, time_step: float):
        """记录步骤性能"""
        self.step_times.append(step_time)
        self.time_steps.append(time_step)

    def get_statistics(self) -> Dict[str, float]:
        """获取性能统计"""
        if not self.step_times:
            return {}

        import numpy as np
        return {
            'avg_step_time': np.mean(self.step_times),
            'max_step_time': np.max(self.step_times),
            'min_step_time': np.min(self.step_times),
            'avg_time_step': np.mean(self.time_steps),
            'total_steps': len(self.step_times)
        }

class EnhancedCouplingManager(CouplingManager):
    """
    增强的耦合管理器（基于adevice_complement4.md规范）

    提供高级耦合算法和智能数据管理
    """

    def __init__(self, coupling_config: Dict[str, Any]):
        """
        初始化增强的耦合管理器

        Args:
            coupling_config: 耦合配置字典
        """
        super().__init__(coupling_config)

        # 增强功能配置
        self.adaptive_time_stepping = coupling_config.get('adaptive_time_stepping', True)
        self.convergence_acceleration = coupling_config.get('convergence_acceleration', True)
        self.load_balancing = coupling_config.get('load_balancing', True)
        self.error_control = coupling_config.get('error_control', True)

        # 自适应时间步长参数
        self.min_time_step = coupling_config.get('min_time_step', 1e-6)
        self.max_time_step = coupling_config.get('max_time_step', 0.1)
        self.time_step_safety_factor = coupling_config.get('time_step_safety_factor', 0.8)
        self.target_error = coupling_config.get('target_error', 1e-4)

        # 收敛加速参数
        self.relaxation_factor = coupling_config.get('relaxation_factor', 0.7)
        self.aitken_acceleration = coupling_config.get('aitken_acceleration', True)
        self.max_coupling_iterations = coupling_config.get('max_coupling_iterations', 10)

        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        self.convergence_history = []
        self.time_step_history = []

        print(f"✅ 增强耦合管理器初始化完成")
        print(f"   自适应时间步长: {'启用' if self.adaptive_time_stepping else '禁用'}")
        print(f"   收敛加速: {'启用' if self.convergence_acceleration else '禁用'}")
        print(f"   负载均衡: {'启用' if self.load_balancing else '禁用'}")

    def execute_enhanced_coupling(self) -> Dict[str, Any]:
        """
        执行增强的耦合计算

        Returns:
            results: 耦合计算结果
        """
        try:
            print(f"🚀 开始增强耦合计算")
            start_time = time.time()

            # 初始化求解器
            self._initialize_enhanced_solvers()

            # 主时间循环
            step_count = 0
            while self.current_time < self.max_time:
                step_start_time = time.time()

                # 自适应时间步长控制
                if self.adaptive_time_stepping:
                    self.time_step = self._compute_adaptive_time_step()

                # 执行耦合步
                coupling_converged = self._execute_enhanced_coupling_step()

                if not coupling_converged:
                    print(f"   ⚠️ 时间步 {step_count} 耦合未收敛，减小时间步长")
                    self.time_step *= 0.5
                    if self.time_step < self.min_time_step:
                        print(f"   ❌ 时间步长过小，计算终止")
                        break
                    continue

                # 更新时间
                self.current_time += self.time_step
                step_count += 1

                # 性能监控
                step_time = time.time() - step_start_time
                self.performance_monitor.record_step(step_time, self.time_step)

                # 输出进度
                if step_count % 10 == 0:
                    progress = self.current_time / self.max_time * 100
                    print(f"   进度: {progress:.1f}% (步数: {step_count}, 时间: {self.current_time:.4f}s)")

            # 收集结果
            total_time = time.time() - start_time
            results = self._collect_enhanced_results(total_time, step_count)

            print(f"✅ 增强耦合计算完成")
            print(f"   总时间: {total_time:.2f}s")
            print(f"   总步数: {step_count}")
            print(f"   平均步长: {self.current_time/step_count:.6f}s")

            return results

        except Exception as e:
            print(f"   ❌ 增强耦合计算失败: {e}")
            raise

    def _initialize_enhanced_solvers(self):
        """初始化增强求解器"""
        try:
            # 负载均衡初始化
            if self.load_balancing:
                self._balance_solver_loads()

            # 初始化所有求解器
            for solver_name, solver in self.solvers.items():
                if hasattr(solver, 'initialize_enhanced'):
                    solver.initialize_enhanced()
                else:
                    solver.initialize()

                print(f"   ✅ 求解器 {solver_name} 初始化完成")

            # 初始化耦合求解器
            for coupling_name, coupling_solver in self.coupling_solvers.items():
                if hasattr(coupling_solver, 'initialize_enhanced'):
                    coupling_solver.initialize_enhanced()
                else:
                    coupling_solver.initialize()

                print(f"   ✅ 耦合求解器 {coupling_name} 初始化完成")

        except Exception as e:
            print(f"   ⚠️ 增强求解器初始化失败: {e}")

    def _execute_enhanced_coupling_step(self) -> bool:
        """执行增强的耦合步"""
        try:
            coupling_iteration = 0
            converged = False

            # 保存上一步的解
            previous_solutions = self._save_current_solutions()

            while coupling_iteration < self.max_coupling_iterations and not converged:
                # 执行各个求解器
                solver_residuals = {}

                for solver_name, solver in self.solvers.items():
                    try:
                        # 更新边界条件
                        self._update_solver_boundary_conditions(solver_name, solver)

                        # 求解
                        if hasattr(solver, 'solve_enhanced'):
                            residual = solver.solve_enhanced(self.time_step)
                        else:
                            residual = solver.solve(self.time_step)

                        solver_residuals[solver_name] = residual

                    except Exception as e:
                        print(f"   ⚠️ 求解器 {solver_name} 求解失败: {e}")
                        return False

                # 执行耦合求解器
                for coupling_name, coupling_solver in self.coupling_solvers.items():
                    try:
                        if hasattr(coupling_solver, 'solve_enhanced'):
                            coupling_solver.solve_enhanced(self.time_step)
                        else:
                            coupling_solver.solve(self.time_step)
                    except Exception as e:
                        print(f"   ⚠️ 耦合求解器 {coupling_name} 求解失败: {e}")
                        return False

                # 检查收敛性
                max_residual = max(solver_residuals.values()) if solver_residuals else 0.0
                converged = max_residual < self.target_error

                # 收敛加速
                if self.convergence_acceleration and not converged:
                    self._apply_convergence_acceleration(coupling_iteration)

                coupling_iteration += 1

                # 记录收敛历史
                self.convergence_history.append({
                    'time': self.current_time,
                    'iteration': coupling_iteration,
                    'residual': max_residual
                })

            if not converged:
                print(f"   ⚠️ 耦合迭代未收敛 (最大残差: {max_residual:.2e})")

            return converged

        except Exception as e:
            print(f"   ❌ 增强耦合步执行失败: {e}")
            return False

    def _compute_adaptive_time_step(self) -> float:
        """计算自适应时间步长"""
        try:
            if len(self.convergence_history) < 2:
                return self.time_step

            # 基于收敛历史调整时间步长
            recent_residuals = [entry['residual'] for entry in self.convergence_history[-5:]]

            if len(recent_residuals) >= 2:
                residual_trend = recent_residuals[-1] / recent_residuals[-2]

                if residual_trend < 0.5:
                    # 收敛快，可以增大时间步长
                    new_time_step = self.time_step * 1.2
                elif residual_trend > 2.0:
                    # 收敛慢，减小时间步长
                    new_time_step = self.time_step * 0.8
                else:
                    # 保持当前时间步长
                    new_time_step = self.time_step

                # 应用安全因子和限制
                new_time_step *= self.time_step_safety_factor
                new_time_step = max(self.min_time_step, min(self.max_time_step, new_time_step))

                return new_time_step

            return self.time_step

        except Exception as e:
            print(f"   ⚠️ 自适应时间步长计算失败: {e}")
            return self.time_step

    def _apply_convergence_acceleration(self, iteration: int):
        """应用收敛加速技术"""
        try:
            if self.aitken_acceleration and iteration >= 2:
                # Aitken加速
                self._apply_aitken_acceleration()
            else:
                # 简单松弛
                self._apply_relaxation()

        except Exception as e:
            print(f"   ⚠️ 收敛加速应用失败: {e}")

    def _apply_aitken_acceleration(self):
        """应用Aitken加速"""
        try:
            # 简化的Aitken加速实现
            if len(self.convergence_history) >= 3:
                r_n = self.convergence_history[-1]['residual']
                r_n1 = self.convergence_history[-2]['residual']
                r_n2 = self.convergence_history[-3]['residual']

                if abs(r_n - 2*r_n1 + r_n2) > 1e-12:
                    aitken_factor = -r_n1 * (r_n - r_n1) / (r_n - 2*r_n1 + r_n2)
                    aitken_factor = max(0.1, min(1.0, aitken_factor))

                    # 更新松弛因子
                    self.relaxation_factor = aitken_factor

        except Exception as e:
            print(f"   ⚠️ Aitken加速失败: {e}")

    def _apply_relaxation(self):
        """应用松弛技术"""
        try:
            # 对所有求解器应用松弛
            for solver_name, solver in self.solvers.items():
                if hasattr(solver, 'apply_relaxation'):
                    solver.apply_relaxation(self.relaxation_factor)

        except Exception as e:
            print(f"   ⚠️ 松弛应用失败: {e}")

    def _balance_solver_loads(self):
        """负载均衡"""
        try:
            # 简化的负载均衡实现
            print(f"   🔄 执行求解器负载均衡...")

            # 估计各求解器的计算负载
            solver_loads = {}
            for solver_name, solver in self.solvers.items():
                if hasattr(solver, 'estimate_computational_load'):
                    load = solver.estimate_computational_load()
                else:
                    load = 1.0  # 默认负载
                solver_loads[solver_name] = load

            # 根据负载调整求解器优先级
            sorted_solvers = sorted(solver_loads.items(), key=lambda x: x[1], reverse=True)

            print(f"   求解器负载排序: {[f'{name}({load:.2f})' for name, load in sorted_solvers]}")

        except Exception as e:
            print(f"   ⚠️ 负载均衡失败: {e}")

    def _save_current_solutions(self) -> Dict[str, Any]:
        """保存当前解"""
        try:
            solutions = {}
            for solver_name, solver in self.solvers.items():
                if hasattr(solver, 'get_current_solution'):
                    solutions[solver_name] = solver.get_current_solution()
            return solutions
        except Exception as e:
            print(f"   ⚠️ 解保存失败: {e}")
            return {}

    def _update_solver_boundary_conditions(self, solver_name: str, solver: Any):
        """更新求解器边界条件"""
        try:
            # 从其他求解器获取边界条件数据
            boundary_data = {}

            for other_name, other_solver in self.solvers.items():
                if other_name != solver_name:
                    if hasattr(other_solver, 'get_boundary_data'):
                        data = other_solver.get_boundary_data(solver_name)
                        if data:
                            boundary_data[other_name] = data

            # 更新边界条件
            if boundary_data and hasattr(solver, 'update_boundary_conditions'):
                solver.update_boundary_conditions(boundary_data)

        except Exception as e:
            print(f"   ⚠️ 边界条件更新失败: {e}")

    def _collect_enhanced_results(self, total_time: float, step_count: int) -> Dict[str, Any]:
        """收集增强结果"""
        try:
            results = {
                'computation_time': total_time,
                'total_steps': step_count,
                'final_time': self.current_time,
                'performance_stats': self.performance_monitor.get_statistics(),
                'convergence_history': self.convergence_history,
                'solver_results': {},
                'coupling_results': {}
            }

            # 收集求解器结果
            for solver_name, solver in self.solvers.items():
                if hasattr(solver, 'get_results'):
                    results['solver_results'][solver_name] = solver.get_results()

            # 收集耦合求解器结果
            for coupling_name, coupling_solver in self.coupling_solvers.items():
                if hasattr(coupling_solver, 'get_results'):
                    results['coupling_results'][coupling_name] = coupling_solver.get_results()

            return results

        except Exception as e:
            print(f"   ⚠️ 结果收集失败: {e}")
            return {'error': str(e)}


def create_enhanced_coupling_manager(config: Dict[str, Any]) -> EnhancedCouplingManager:
    """创建增强的耦合管理器"""
    return EnhancedCouplingManager(config)
