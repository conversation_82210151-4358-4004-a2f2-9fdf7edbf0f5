"""
求解器工厂
=========

提供统一的求解器创建接口

基于adevice_complement4.md技术规范实现
"""

from typing import Dict, Any, Optional, Type
from abc import ABC, abstractmethod
import warnings


class SolverFactory:
    """
    求解器工厂（基于adevice_complement4.md规范）
    
    提供统一的求解器创建和管理接口
    """
    
    def __init__(self):
        """初始化求解器工厂"""
        self._aerodynamic_solvers = {}
        self._acoustic_solvers = {}
        self._linear_solvers = {}
        self._coupling_solvers = {}
        
        # 注册默认求解器
        self._register_default_solvers()
    
    def _register_default_solvers(self):
        """注册默认求解器"""
        try:
            # 气动求解器
            self._aerodynamic_solvers.update({
                'bemt': 'core.aerodynamics.solvers.bemt_solver.BEMTSolver',
                'uvlm': 'core.aerodynamics.solvers.uvlm_solver.UVLMSolver',
                'panel': 'core.aerodynamics.solvers.panel_solver.PanelSolver'
            })
            
            # 声学求解器
            self._acoustic_solvers.update({
                'fwh': 'core.acoustics.solvers.fwh_solver.FWHSolver',
                'bpm': 'core.acoustics.bpm_noise_model.BPMNoiseModel',
                'kirchhoff': 'core.acoustics.solvers.kirchhoff_solver.KirchhoffSolver'
            })
            
            # 线性求解器
            self._linear_solvers.update({
                'direct': 'core.numerical.linear_solvers.DirectLinearSolver',
                'iterative': 'core.numerical.linear_solvers.IterativeLinearSolver',
                'gpu': 'core.numerical.linear_solvers.GPULinearSolver',
                'block': 'core.numerical.linear_solvers.BlockLinearSolver',
                'multilevel': 'core.numerical.linear_solvers.MultiLevelLinearSolver'
            })
            
            # 耦合求解器
            self._coupling_solvers.update({
                'aero_acoustic': 'core.coupling.aero_acoustic_coupling.AeroAcousticCoupling'
            })
            
        except Exception as e:
            warnings.warn(f"默认求解器注册失败: {e}")
    
    def create_aerodynamic_solver(self, solver_type: str, config: Dict[str, Any]) -> Any:
        """
        创建气动求解器
        
        Args:
            solver_type: 求解器类型
            config: 求解器配置
            
        Returns:
            solver: 气动求解器实例
        """
        try:
            if solver_type not in self._aerodynamic_solvers:
                raise ValueError(f"不支持的气动求解器类型: {solver_type}")
            
            solver_class_path = self._aerodynamic_solvers[solver_type]
            solver_class = self._import_class(solver_class_path)
            
            # 创建求解器实例
            solver = solver_class(config)
            
            print(f"✅ 创建气动求解器: {solver_type}")
            return solver
            
        except Exception as e:
            print(f"   ❌ 气动求解器创建失败: {e}")
            raise
    
    def create_acoustic_solver(self, solver_type: str, config: Dict[str, Any]) -> Any:
        """
        创建声学求解器
        
        Args:
            solver_type: 求解器类型
            config: 求解器配置
            
        Returns:
            solver: 声学求解器实例
        """
        try:
            if solver_type not in self._acoustic_solvers:
                raise ValueError(f"不支持的声学求解器类型: {solver_type}")
            
            solver_class_path = self._acoustic_solvers[solver_type]
            solver_class = self._import_class(solver_class_path)
            
            # 创建求解器实例
            solver = solver_class(config)
            
            print(f"✅ 创建声学求解器: {solver_type}")
            return solver
            
        except Exception as e:
            print(f"   ❌ 声学求解器创建失败: {e}")
            raise
    
    def create_linear_solver(self, solver_type: str, config: Dict[str, Any]) -> Any:
        """
        创建线性求解器
        
        Args:
            solver_type: 求解器类型
            config: 求解器配置
            
        Returns:
            solver: 线性求解器实例
        """
        try:
            if solver_type not in self._linear_solvers:
                raise ValueError(f"不支持的线性求解器类型: {solver_type}")
            
            solver_class_path = self._linear_solvers[solver_type]
            solver_class = self._import_class(solver_class_path)
            
            # 创建求解器实例
            solver = solver_class(config)
            
            print(f"✅ 创建线性求解器: {solver_type}")
            return solver
            
        except Exception as e:
            print(f"   ❌ 线性求解器创建失败: {e}")
            raise
    
    def create_coupling_solver(self, solver_type: str, config: Dict[str, Any]) -> Any:
        """
        创建耦合求解器
        
        Args:
            solver_type: 求解器类型
            config: 求解器配置
            
        Returns:
            solver: 耦合求解器实例
        """
        try:
            if solver_type not in self._coupling_solvers:
                raise ValueError(f"不支持的耦合求解器类型: {solver_type}")
            
            solver_class_path = self._coupling_solvers[solver_type]
            solver_class = self._import_class(solver_class_path)
            
            # 创建求解器实例
            solver = solver_class(config)
            
            print(f"✅ 创建耦合求解器: {solver_type}")
            return solver
            
        except Exception as e:
            print(f"   ❌ 耦合求解器创建失败: {e}")
            raise
    
    def register_solver(self, category: str, solver_type: str, class_path: str):
        """
        注册新的求解器
        
        Args:
            category: 求解器类别 ('aerodynamic', 'acoustic', 'linear', 'coupling')
            solver_type: 求解器类型
            class_path: 类路径
        """
        try:
            if category == 'aerodynamic':
                self._aerodynamic_solvers[solver_type] = class_path
            elif category == 'acoustic':
                self._acoustic_solvers[solver_type] = class_path
            elif category == 'linear':
                self._linear_solvers[solver_type] = class_path
            elif category == 'coupling':
                self._coupling_solvers[solver_type] = class_path
            else:
                raise ValueError(f"不支持的求解器类别: {category}")
            
            print(f"✅ 注册求解器: {category}.{solver_type}")
            
        except Exception as e:
            print(f"   ❌ 求解器注册失败: {e}")
    
    def list_available_solvers(self) -> Dict[str, list]:
        """
        列出可用的求解器
        
        Returns:
            solvers: 可用求解器字典
        """
        return {
            'aerodynamic': list(self._aerodynamic_solvers.keys()),
            'acoustic': list(self._acoustic_solvers.keys()),
            'linear': list(self._linear_solvers.keys()),
            'coupling': list(self._coupling_solvers.keys())
        }
    
    def _import_class(self, class_path: str) -> Type:
        """
        动态导入类
        
        Args:
            class_path: 类路径
            
        Returns:
            class_type: 类类型
        """
        try:
            module_path, class_name = class_path.rsplit('.', 1)
            
            # 动态导入模块
            import importlib
            module = importlib.import_module(module_path)
            
            # 获取类
            class_type = getattr(module, class_name)
            
            return class_type
            
        except Exception as e:
            raise ImportError(f"无法导入类 {class_path}: {e}")
    
    def create_solver_suite(self, suite_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建求解器套件
        
        Args:
            suite_config: 套件配置
            
        Returns:
            solver_suite: 求解器套件
        """
        try:
            solver_suite = {}
            
            # 创建气动求解器
            if 'aerodynamic' in suite_config:
                aero_config = suite_config['aerodynamic']
                solver_type = aero_config.get('type', 'bemt')
                solver_suite['aerodynamic'] = self.create_aerodynamic_solver(
                    solver_type, aero_config.get('config', {})
                )
            
            # 创建声学求解器
            if 'acoustic' in suite_config:
                acoustic_config = suite_config['acoustic']
                solver_type = acoustic_config.get('type', 'fwh')
                solver_suite['acoustic'] = self.create_acoustic_solver(
                    solver_type, acoustic_config.get('config', {})
                )
            
            # 创建线性求解器
            if 'linear' in suite_config:
                linear_config = suite_config['linear']
                solver_type = linear_config.get('type', 'direct')
                solver_suite['linear'] = self.create_linear_solver(
                    solver_type, linear_config.get('config', {})
                )
            
            # 创建耦合求解器
            if 'coupling' in suite_config:
                coupling_config = suite_config['coupling']
                solver_type = coupling_config.get('type', 'aero_acoustic')
                solver_suite['coupling'] = self.create_coupling_solver(
                    solver_type, coupling_config.get('config', {})
                )
            
            print(f"✅ 创建求解器套件，包含 {len(solver_suite)} 个求解器")
            return solver_suite
            
        except Exception as e:
            print(f"   ❌ 求解器套件创建失败: {e}")
            raise


# 全局求解器工厂实例
_global_solver_factory = None

def get_solver_factory() -> SolverFactory:
    """获取全局求解器工厂实例"""
    global _global_solver_factory
    if _global_solver_factory is None:
        _global_solver_factory = SolverFactory()
    return _global_solver_factory

def create_solver(category: str, solver_type: str, config: Dict[str, Any]) -> Any:
    """
    便捷的求解器创建函数
    
    Args:
        category: 求解器类别
        solver_type: 求解器类型
        config: 求解器配置
        
    Returns:
        solver: 求解器实例
    """
    factory = get_solver_factory()
    
    if category == 'aerodynamic':
        return factory.create_aerodynamic_solver(solver_type, config)
    elif category == 'acoustic':
        return factory.create_acoustic_solver(solver_type, config)
    elif category == 'linear':
        return factory.create_linear_solver(solver_type, config)
    elif category == 'coupling':
        return factory.create_coupling_solver(solver_type, config)
    else:
        raise ValueError(f"不支持的求解器类别: {category}")
