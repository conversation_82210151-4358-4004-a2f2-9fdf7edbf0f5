"""
组件工厂
=======

提供统一的系统组件创建接口

基于adevice_complement4.md技术规范实现
"""

from typing import Dict, Any, Optional, Type
import warnings


class ComponentFactory:
    """
    组件工厂（基于adevice_complement4.md规范）
    
    提供统一的系统组件创建和管理接口
    """
    
    def __init__(self):
        """初始化组件工厂"""
        self._mesh_generators = {}
        self._data_managers = {}
        self._coupling_managers = {}
        self._gpu_accelerators = {}
        
        # 注册默认组件
        self._register_default_components()
    
    def _register_default_components(self):
        """注册默认组件"""
        try:
            # 网格生成器
            self._mesh_generators.update({
                'structured': 'core.geometry.mesh_generator.MeshGenerator',
                'unstructured': 'core.geometry.mesh_generator.MeshGenerator',
                'adaptive': 'core.geometry.mesh_generator.MeshGenerator'
            })
            
            # 数据管理器
            self._data_managers.update({
                'time_history': 'core.data.time_history_manager.TimeHistoryManager',
                'field_data': 'core.data.field_data_manager.FieldDataManager',
                'result_data': 'core.data.result_data_manager.ResultDataManager'
            })
            
            # 耦合管理器
            self._coupling_managers.update({
                'aero_acoustic': 'core.coupling.aero_acoustic_coupling.AeroAcousticCoupling',
                'fluid_structure': 'core.coupling.fluid_structure_coupling.FluidStructureCoupling'
            })
            
            # GPU加速器
            self._gpu_accelerators.update({
                'biot_savart': 'core.gpu.gpu_biot_savart.GPUBiotSavart',
                'matrix_ops': 'core.gpu.gpu_matrix_operations.GPUMatrixOperations',
                'integration': 'core.gpu.gpu_integration.GPUIntegration',
                'memory_manager': 'core.gpu.gpu_memory_manager.GPUMemoryManager'
            })
            
        except Exception as e:
            warnings.warn(f"默认组件注册失败: {e}")
    
    def create_mesh_generator(self, generator_type: str, config: Dict[str, Any]) -> Any:
        """
        创建网格生成器
        
        Args:
            generator_type: 生成器类型
            config: 生成器配置
            
        Returns:
            generator: 网格生成器实例
        """
        try:
            if generator_type not in self._mesh_generators:
                raise ValueError(f"不支持的网格生成器类型: {generator_type}")
            
            generator_class_path = self._mesh_generators[generator_type]
            generator_class = self._import_class(generator_class_path)
            
            # 设置网格类型
            config['mesh_type'] = generator_type
            
            # 创建生成器实例
            generator = generator_class(config)
            
            print(f"✅ 创建网格生成器: {generator_type}")
            return generator
            
        except Exception as e:
            print(f"   ❌ 网格生成器创建失败: {e}")
            raise
    
    def create_data_manager(self, manager_type: str, config: Dict[str, Any]) -> Any:
        """
        创建数据管理器
        
        Args:
            manager_type: 管理器类型
            config: 管理器配置
            
        Returns:
            manager: 数据管理器实例
        """
        try:
            if manager_type not in self._data_managers:
                raise ValueError(f"不支持的数据管理器类型: {manager_type}")
            
            manager_class_path = self._data_managers[manager_type]
            manager_class = self._import_class(manager_class_path)
            
            # 创建管理器实例
            manager = manager_class(config)
            
            print(f"✅ 创建数据管理器: {manager_type}")
            return manager
            
        except Exception as e:
            print(f"   ❌ 数据管理器创建失败: {e}")
            raise
    
    def create_coupling_manager(self, manager_type: str, config: Dict[str, Any]) -> Any:
        """
        创建耦合管理器
        
        Args:
            manager_type: 管理器类型
            config: 管理器配置
            
        Returns:
            manager: 耦合管理器实例
        """
        try:
            if manager_type not in self._coupling_managers:
                raise ValueError(f"不支持的耦合管理器类型: {manager_type}")
            
            manager_class_path = self._coupling_managers[manager_type]
            manager_class = self._import_class(manager_class_path)
            
            # 创建管理器实例
            manager = manager_class(config)
            
            print(f"✅ 创建耦合管理器: {manager_type}")
            return manager
            
        except Exception as e:
            print(f"   ❌ 耦合管理器创建失败: {e}")
            raise
    
    def create_gpu_accelerator(self, accelerator_type: str, **kwargs) -> Any:
        """
        创建GPU加速器
        
        Args:
            accelerator_type: 加速器类型
            **kwargs: 加速器参数
            
        Returns:
            accelerator: GPU加速器实例
        """
        try:
            if accelerator_type not in self._gpu_accelerators:
                raise ValueError(f"不支持的GPU加速器类型: {accelerator_type}")
            
            accelerator_class_path = self._gpu_accelerators[accelerator_type]
            accelerator_class = self._import_class(accelerator_class_path)
            
            # 创建加速器实例
            accelerator = accelerator_class(**kwargs)
            
            print(f"✅ 创建GPU加速器: {accelerator_type}")
            return accelerator
            
        except Exception as e:
            print(f"   ❌ GPU加速器创建失败: {e}")
            raise
    
    def register_component(self, category: str, component_type: str, class_path: str):
        """
        注册新的组件
        
        Args:
            category: 组件类别 ('mesh', 'data', 'coupling', 'gpu')
            component_type: 组件类型
            class_path: 类路径
        """
        try:
            if category == 'mesh':
                self._mesh_generators[component_type] = class_path
            elif category == 'data':
                self._data_managers[component_type] = class_path
            elif category == 'coupling':
                self._coupling_managers[component_type] = class_path
            elif category == 'gpu':
                self._gpu_accelerators[component_type] = class_path
            else:
                raise ValueError(f"不支持的组件类别: {category}")
            
            print(f"✅ 注册组件: {category}.{component_type}")
            
        except Exception as e:
            print(f"   ❌ 组件注册失败: {e}")
    
    def list_available_components(self) -> Dict[str, list]:
        """
        列出可用的组件
        
        Returns:
            components: 可用组件字典
        """
        return {
            'mesh': list(self._mesh_generators.keys()),
            'data': list(self._data_managers.keys()),
            'coupling': list(self._coupling_managers.keys()),
            'gpu': list(self._gpu_accelerators.keys())
        }
    
    def _import_class(self, class_path: str) -> Type:
        """
        动态导入类
        
        Args:
            class_path: 类路径
            
        Returns:
            class_type: 类类型
        """
        try:
            module_path, class_name = class_path.rsplit('.', 1)
            
            # 动态导入模块
            import importlib
            module = importlib.import_module(module_path)
            
            # 获取类
            class_type = getattr(module, class_name)
            
            return class_type
            
        except Exception as e:
            raise ImportError(f"无法导入类 {class_path}: {e}")
    
    def create_component_suite(self, suite_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建组件套件
        
        Args:
            suite_config: 套件配置
            
        Returns:
            component_suite: 组件套件
        """
        try:
            component_suite = {}
            
            # 创建网格生成器
            if 'mesh' in suite_config:
                mesh_config = suite_config['mesh']
                generator_type = mesh_config.get('type', 'structured')
                component_suite['mesh'] = self.create_mesh_generator(
                    generator_type, mesh_config.get('config', {})
                )
            
            # 创建数据管理器
            if 'data' in suite_config:
                data_config = suite_config['data']
                manager_type = data_config.get('type', 'time_history')
                component_suite['data'] = self.create_data_manager(
                    manager_type, data_config.get('config', {})
                )
            
            # 创建耦合管理器
            if 'coupling' in suite_config:
                coupling_config = suite_config['coupling']
                manager_type = coupling_config.get('type', 'aero_acoustic')
                component_suite['coupling'] = self.create_coupling_manager(
                    manager_type, coupling_config.get('config', {})
                )
            
            # 创建GPU加速器
            if 'gpu' in suite_config:
                gpu_config = suite_config['gpu']
                accelerator_type = gpu_config.get('type', 'biot_savart')
                accelerator_params = gpu_config.get('params', {})
                component_suite['gpu'] = self.create_gpu_accelerator(
                    accelerator_type, **accelerator_params
                )
            
            print(f"✅ 创建组件套件，包含 {len(component_suite)} 个组件")
            return component_suite
            
        except Exception as e:
            print(f"   ❌ 组件套件创建失败: {e}")
            raise


# 全局组件工厂实例
_global_component_factory = None

def get_component_factory() -> ComponentFactory:
    """获取全局组件工厂实例"""
    global _global_component_factory
    if _global_component_factory is None:
        _global_component_factory = ComponentFactory()
    return _global_component_factory

def create_component(category: str, component_type: str, config: Dict[str, Any] = None, **kwargs) -> Any:
    """
    便捷的组件创建函数
    
    Args:
        category: 组件类别
        component_type: 组件类型
        config: 组件配置
        **kwargs: 额外参数
        
    Returns:
        component: 组件实例
    """
    factory = get_component_factory()
    
    if category == 'mesh':
        return factory.create_mesh_generator(component_type, config or {})
    elif category == 'data':
        return factory.create_data_manager(component_type, config or {})
    elif category == 'coupling':
        return factory.create_coupling_manager(component_type, config or {})
    elif category == 'gpu':
        return factory.create_gpu_accelerator(component_type, **kwargs)
    else:
        raise ValueError(f"不支持的组件类别: {category}")
