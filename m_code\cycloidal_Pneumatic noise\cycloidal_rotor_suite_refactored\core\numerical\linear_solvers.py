"""
线性方程组求解器 - 完全复刻原始算法
================================

实现多种线性方程组求解方法，包括直接法和迭代法。
基于原始 cycloidal_rotor_suite 项目的完整实现。

主要功能：
- 直接法（LU分解、Cholesky分解）
- 迭代法（GMRES、BiCGSTAB、CG）
- 预条件技术（对角、ILU、AMG）
- 自适应求解策略
- 条件数监控和数值稳定性控制

基于原始项目的完整实现，确保与原始算法在数值层面完全一致。

作者: Augment Agent
日期: 2025-08-03
"""

import warnings
from typing import Dict, Optional, Tuple, Union
from abc import ABC, abstractmethod

import numpy as np

try:
    from scipy.sparse import diags, csc_matrix
    from scipy.sparse.linalg import gmres, bicgstab, cg, spsolve
    from scipy.linalg import solve, lstsq, LinAlgError
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    warnings.warn("SciPy不可用，将使用简化的线性求解方法")


class LinearSolverBase(ABC):
    """线性求解器基类 - 完全复刻原始实现"""
    
    def __init__(self, config: Dict):
        """
        初始化线性求解器
        
        Args:
            config: 求解器配置
        """
        self.config = config
        self.tolerance = config.get('tolerance', 1e-6)
        self.max_iterations = config.get('max_iterations', 1000)
        self.numerical_tolerance = 1e-12
        
        # 性能统计
        self.solve_count = 0
        self.total_iterations = 0
        self.total_solve_time = 0.0
    
    @abstractmethod
    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解线性方程组 Ax = b
        
        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N]
            
        Returns:
            x: 解向量 [N]
        """
        pass
    
    def get_condition_number(self, A: np.ndarray) -> float:
        """计算条件数"""
        try:
            return np.linalg.cond(A)
        except Exception:
            return 1e16  # 返回一个大数表示病态


class DirectLinearSolver(LinearSolverBase):
    """
    直接线性求解器 - 完全复刻原始算法
    
    使用LU分解等直接方法求解线性方程组
    """
    
    def __init__(self, config: Dict):
        """
        初始化直接求解器
        
        Args:
            config: 求解器配置
        """
        super().__init__(config)
        self.method = config.get('direct_method', 'lu')  # 'lu', 'cholesky', 'qr'
        self.use_iterative_refinement = config.get('use_iterative_refinement', False)
        self.refinement_steps = config.get('refinement_steps', 3)
        
        print(f"✅ 直接线性求解器初始化完成")
        print(f"   方法: {self.method}")
        print(f"   迭代改进: {'启用' if self.use_iterative_refinement else '禁用'}")
    
    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解线性方程组 - 完全复刻原始算法
        
        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N]
            
        Returns:
            x: 解向量 [N]
        """
        self.solve_count += 1
        
        try:
            if self.method == 'lu':
                x = self._lu_solve(A, b)
            elif self.method == 'cholesky':
                x = self._cholesky_solve(A, b)
            elif self.method == 'qr':
                x = self._qr_solve(A, b)
            else:
                raise ValueError(f"不支持的直接求解方法: {self.method}")
            
            # 迭代改进
            if self.use_iterative_refinement:
                x = self._iterative_refinement(A, b, x)
            
            return x
            
        except Exception as e:
            print(f"警告：直接求解失败 {e}，使用最小二乘法")
            return self._fallback_solve(A, b)
    
    def _lu_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """LU分解求解"""
        if SCIPY_AVAILABLE:
            return solve(A, b)
        else:
            return np.linalg.solve(A, b)
    
    def _cholesky_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """Cholesky分解求解（适用于对称正定矩阵）"""
        try:
            L = np.linalg.cholesky(A)
            # 前向替换求解 Ly = b
            y = np.linalg.solve(L, b)
            # 后向替换求解 L^T x = y
            x = np.linalg.solve(L.T, y)
            return x
        except np.linalg.LinAlgError:
            # 如果不是正定矩阵，回退到LU分解
            return self._lu_solve(A, b)
    
    def _qr_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """QR分解求解"""
        Q, R = np.linalg.qr(A)
        y = np.dot(Q.T, b)
        x = np.linalg.solve(R, y)
        return x
    
    def _iterative_refinement(self, A: np.ndarray, b: np.ndarray, x: np.ndarray) -> np.ndarray:
        """迭代改进 - 完全复刻原始算法"""
        for step in range(self.refinement_steps):
            # 计算残差
            residual = b - np.dot(A, x)
            
            # 检查收敛
            residual_norm = np.linalg.norm(residual)
            if residual_norm < self.tolerance:
                break
            
            # 求解修正方程
            try:
                correction = self._lu_solve(A, residual)
                x = x + correction
            except Exception:
                break  # 如果修正失败，停止改进
        
        return x
    
    def _fallback_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """备用求解方法"""
        try:
            if SCIPY_AVAILABLE:
                return lstsq(A, b)[0]
            else:
                return np.linalg.lstsq(A, b, rcond=None)[0]
        except Exception:
            # 最后的备用方案：伪逆
            A_pinv = np.linalg.pinv(A)
            return np.dot(A_pinv, b)


class IterativeLinearSolver(LinearSolverBase):
    """
    迭代线性求解器 - 完全复刻原始算法
    
    使用GMRES、BiCGSTAB等迭代方法求解线性方程组
    """
    
    def __init__(self, config: Dict):
        """
        初始化迭代求解器
        
        Args:
            config: 求解器配置
        """
        super().__init__(config)
        self.method = config.get('iterative_method', 'gmres')  # 'gmres', 'bicgstab', 'cg'
        self.restart = config.get('restart', 30)
        self.use_preconditioner = config.get('use_preconditioner', True)
        self.preconditioner_type = config.get('preconditioner_type', 'diagonal')
        
        print(f"✅ 迭代线性求解器初始化完成")
        print(f"   方法: {self.method}")
        print(f"   预条件: {self.preconditioner_type if self.use_preconditioner else '无'}")
    
    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解线性方程组 - 完全复刻原始算法
        
        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N]
            
        Returns:
            x: 解向量 [N]
        """
        if not SCIPY_AVAILABLE:
            print("警告：SciPy不可用，使用直接求解")
            return np.linalg.solve(A, b)
        
        self.solve_count += 1
        
        # 创建预条件子
        M = self._create_preconditioner(A) if self.use_preconditioner else None
        
        try:
            if self.method == 'gmres':
                x, info = gmres(
                    A, b,
                    tol=self.tolerance,
                    maxiter=self.max_iterations,
                    restart=self.restart,
                    M=M
                )
            elif self.method == 'bicgstab':
                x, info = bicgstab(
                    A, b,
                    tol=self.tolerance,
                    maxiter=self.max_iterations,
                    M=M
                )
            elif self.method == 'cg':
                x, info = cg(
                    A, b,
                    tol=self.tolerance,
                    maxiter=self.max_iterations,
                    M=M
                )
            else:
                raise ValueError(f"不支持的迭代方法: {self.method}")
            
            if info == 0:
                print(f"   {self.method.upper()}收敛")
            else:
                print(f"   {self.method.upper()}未完全收敛，info: {info}")
            
            return x
            
        except Exception as e:
            print(f"警告：迭代求解失败 {e}，使用直接求解")
            return np.linalg.solve(A, b)
    
    def _create_preconditioner(self, A: np.ndarray):
        """创建预条件子 - 完全复刻原始算法"""
        if self.preconditioner_type == 'diagonal':
            # 对角预条件子（Jacobi预条件）
            diag_elements = np.diag(A)
            # 避免零对角元
            diag_elements = np.where(np.abs(diag_elements) < self.numerical_tolerance, 
                                   1.0, diag_elements)
            return diags(1.0 / diag_elements)
        
        elif self.preconditioner_type == 'ilu':
            # ILU预条件子（需要更复杂的实现）
            try:
                from scipy.sparse.linalg import spilu
                A_sparse = csc_matrix(A)
                ilu = spilu(A_sparse)
                return ilu
            except ImportError:
                print("警告：无法创建ILU预条件子，使用对角预条件")
                return self._create_diagonal_preconditioner(A)
        
        else:
            return None


class AdaptiveLinearSolver(LinearSolverBase):
    """
    自适应线性求解器 - 完全复刻原始算法
    
    根据矩阵特性自动选择最优求解方法
    """
    
    def __init__(self, config: Dict):
        """
        初始化自适应求解器
        
        Args:
            config: 求解器配置
        """
        super().__init__(config)
        self.size_threshold = config.get('size_threshold', 100)
        self.condition_threshold = config.get('condition_threshold', 1e12)
        
        # 创建子求解器
        self.direct_solver = DirectLinearSolver(config)
        self.iterative_solver = IterativeLinearSolver(config)
        
        print(f"✅ 自适应线性求解器初始化完成")
        print(f"   尺寸阈值: {self.size_threshold}")
        print(f"   条件数阈值: {self.condition_threshold:.2e}")
    
    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解线性方程组 - 完全复刻原始算法
        
        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N]
            
        Returns:
            x: 解向量 [N]
        """
        n = A.shape[0]
        
        # 计算条件数（对小矩阵）
        if n <= self.size_threshold:
            condition_number = self.get_condition_number(A)
        else:
            condition_number = 1e16  # 假设大矩阵条件数较差
        
        # 自适应选择求解方法
        if n <= self.size_threshold and condition_number < self.condition_threshold:
            # 小矩阵且条件数好：使用直接法
            print(f"   使用直接法求解 (n={n}, cond={condition_number:.2e})")
            return self.direct_solver.solve(A, b)
        else:
            # 大矩阵或条件数差：使用迭代法
            print(f"   使用迭代法求解 (n={n}, cond={condition_number:.2e})")
            return self.iterative_solver.solve(A, b)


# 工厂函数
def create_linear_solver(solver_type: str, config: Dict) -> LinearSolverBase:
    """
    创建线性求解器
    
    Args:
        solver_type: 求解器类型 ('direct', 'iterative', 'adaptive')
        config: 求解器配置
        
    Returns:
        solver: 线性求解器实例
    """
    if solver_type == 'direct':
        return DirectLinearSolver(config)
    elif solver_type == 'iterative':
        return IterativeLinearSolver(config)
    elif solver_type == 'adaptive':
        return AdaptiveLinearSolver(config)
    else:
        raise ValueError(f"不支持的求解器类型: {solver_type}")


def create_default_linear_solver(config: Optional[Dict] = None) -> AdaptiveLinearSolver:
    """创建默认的自适应线性求解器"""
    if config is None:
        config = {
            'tolerance': 1e-6,
            'max_iterations': 1000,
            'size_threshold': 100,
            'condition_threshold': 1e12,
            'direct_method': 'lu',
            'iterative_method': 'gmres',
            'use_preconditioner': True,
            'preconditioner_type': 'diagonal'
        }
    
    return AdaptiveLinearSolver(config)


# ==================== 增强的线性求解器（基于adevice_complement4.md规范） ====================

class GPULinearSolver(LinearSolverBase):
    """
    GPU加速线性求解器（基于adevice_complement4.md规范）

    使用GPU加速大规模线性方程组求解
    """

    def __init__(self, config: Dict):
        """
        初始化GPU线性求解器

        Args:
            config: 求解器配置
        """
        super().__init__(config)

        self.backend = config.get('gpu_backend', 'auto')
        self.device_id = config.get('device_id', 0)
        self.use_gpu = config.get('use_gpu', True)
        self.fallback_to_cpu = config.get('fallback_to_cpu', True)

        # GPU模块
        self.gpu_matrix_ops = None
        self._initialize_gpu()

        print(f"✅ GPU线性求解器初始化完成")
        print(f"   GPU后端: {self.backend}")
        print(f"   GPU可用: {'是' if self.gpu_matrix_ops and self.gpu_matrix_ops.is_available else '否'}")

    def _initialize_gpu(self):
        """初始化GPU计算模块"""
        try:
            from ..gpu.gpu_matrix_operations import create_gpu_matrix_operations
            self.gpu_matrix_ops = create_gpu_matrix_operations(self.backend, self.device_id)
        except ImportError:
            print("   ⚠️ GPU模块不可用，将使用CPU计算")
            self.gpu_matrix_ops = None

    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解线性方程组（GPU加速）

        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N] 或 [N, M]

        Returns:
            x: 解向量 [N] 或 [N, M]
        """
        import time
        start_time = time.time()

        try:
            # 检查矩阵条件
            condition_number = self._compute_condition_number(A)

            if condition_number > 1e12:
                print(f"   ⚠️ 矩阵条件数过大: {condition_number:.2e}")

            # 尝试GPU求解
            if (self.gpu_matrix_ops and self.gpu_matrix_ops.is_available and
                self.use_gpu and A.shape[0] > 100):

                try:
                    x = self.gpu_matrix_ops.solve_linear_system(A, b)

                    # 验证解的质量
                    if self._verify_solution_quality(A, b, x):
                        solve_time = time.time() - start_time
                        self._update_statistics(solve_time, len(A))
                        return x
                    else:
                        print("   ⚠️ GPU解质量不佳，回退到CPU")

                except Exception as e:
                    print(f"   ⚠️ GPU求解失败: {e}")

            # CPU回退求解
            if self.fallback_to_cpu:
                x = self._cpu_solve(A, b)
                solve_time = time.time() - start_time
                self._update_statistics(solve_time, len(A))
                return x
            else:
                raise RuntimeError("GPU求解失败且CPU回退被禁用")

        except Exception as e:
            print(f"   ❌ 线性求解失败: {e}")
            raise

    def _cpu_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """CPU回退求解"""
        try:
            if SCIPY_AVAILABLE:
                return solve(A, b)
            else:
                return np.linalg.solve(A, b)
        except Exception:
            # 最后的备用方案
            return np.linalg.lstsq(A, b, rcond=None)[0]

    def _verify_solution_quality(self, A: np.ndarray, b: np.ndarray, x: np.ndarray) -> bool:
        """验证解的质量"""
        try:
            # 计算残差
            residual = np.linalg.norm(A @ x - b)
            relative_residual = residual / (np.linalg.norm(b) + 1e-12)

            # 质量阈值
            quality_threshold = max(self.tolerance * 100, 1e-3)

            return relative_residual < quality_threshold

        except Exception:
            return False

    def _update_statistics(self, solve_time: float, matrix_size: int):
        """更新求解统计"""
        self.solve_count += 1
        self.total_solve_time += solve_time

        if hasattr(self, 'solve_times'):
            self.solve_times.append(solve_time)
            self.matrix_sizes.append(matrix_size)
        else:
            self.solve_times = [solve_time]
            self.matrix_sizes = [matrix_size]

class BlockLinearSolver(LinearSolverBase):
    """
    分块线性求解器（基于adevice_complement4.md规范）

    用于求解大规模分块结构的线性方程组
    """

    def __init__(self, config: Dict):
        """
        初始化分块线性求解器

        Args:
            config: 求解器配置
        """
        super().__init__(config)

        self.block_size = config.get('block_size', 1000)
        self.overlap_size = config.get('overlap_size', 100)
        self.use_schwarz = config.get('use_schwarz', True)
        self.max_schwarz_iterations = config.get('max_schwarz_iterations', 10)

        # 子求解器
        self.sub_solver_config = config.get('sub_solver_config', {
            'tolerance': self.tolerance * 0.1,
            'max_iterations': 500
        })

        print(f"✅ 分块线性求解器初始化完成")
        print(f"   分块大小: {self.block_size}")
        print(f"   重叠大小: {self.overlap_size}")
        print(f"   Schwarz方法: {'启用' if self.use_schwarz else '禁用'}")

    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        求解分块线性方程组

        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N]

        Returns:
            x: 解向量 [N]
        """
        import time
        start_time = time.time()

        try:
            n = A.shape[0]

            # 如果矩阵较小，直接求解
            if n <= self.block_size:
                return self._direct_solve(A, b)

            # 分块求解
            if self.use_schwarz:
                x = self._schwarz_solve(A, b)
            else:
                x = self._block_diagonal_solve(A, b)

            solve_time = time.time() - start_time
            self._update_statistics(solve_time, n)

            return x

        except Exception as e:
            print(f"   ❌ 分块求解失败: {e}")
            # 回退到直接求解
            return self._direct_solve(A, b)

    def _schwarz_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """Schwarz交替方法求解"""
        n = A.shape[0]
        x = np.zeros(n)

        # 创建分块
        blocks = self._create_overlapping_blocks(n)

        for iteration in range(self.max_schwarz_iterations):
            x_old = x.copy()

            # 对每个分块求解
            for block_indices in blocks:
                # 提取子矩阵和子向量
                A_sub = A[np.ix_(block_indices, block_indices)]
                b_sub = b[block_indices] - A[np.ix_(block_indices, np.arange(n))] @ x
                b_sub += A_sub @ x[block_indices]  # 修正当前分块的贡献

                # 求解子问题
                try:
                    x_sub = self._direct_solve(A_sub, b_sub)
                    x[block_indices] = x_sub
                except Exception as e:
                    print(f"   ⚠️ 分块 {len(block_indices)} 求解失败: {e}")
                    continue

            # 检查收敛性
            residual = np.linalg.norm(x - x_old)
            if residual < self.tolerance:
                print(f"   Schwarz方法在 {iteration + 1} 次迭代后收敛")
                break

        return x

    def _block_diagonal_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """分块对角求解"""
        n = A.shape[0]
        x = np.zeros(n)

        # 创建非重叠分块
        blocks = self._create_non_overlapping_blocks(n)

        for block_indices in blocks:
            # 提取子矩阵和子向量
            A_sub = A[np.ix_(block_indices, block_indices)]
            b_sub = b[block_indices]

            # 求解子问题
            try:
                x_sub = self._direct_solve(A_sub, b_sub)
                x[block_indices] = x_sub
            except Exception as e:
                print(f"   ⚠️ 分块 {len(block_indices)} 求解失败: {e}")
                # 使用简单的对角近似
                diag_A = np.diag(A_sub)
                x[block_indices] = b_sub / (diag_A + 1e-12)

        return x

    def _create_overlapping_blocks(self, n: int) -> list:
        """创建重叠分块"""
        blocks = []
        start = 0

        while start < n:
            end = min(start + self.block_size, n)

            # 添加重叠
            if end < n:
                end = min(end + self.overlap_size, n)

            block_indices = list(range(start, end))
            blocks.append(block_indices)

            # 下一个分块的起始位置
            start = start + self.block_size - self.overlap_size
            if start >= n:
                break

        return blocks

    def _create_non_overlapping_blocks(self, n: int) -> list:
        """创建非重叠分块"""
        blocks = []
        start = 0

        while start < n:
            end = min(start + self.block_size, n)
            block_indices = list(range(start, end))
            blocks.append(block_indices)
            start = end

        return blocks

    def _direct_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """直接求解子问题"""
        try:
            if SCIPY_AVAILABLE:
                return solve(A, b)
            else:
                return np.linalg.solve(A, b)
        except Exception:
            return np.linalg.lstsq(A, b, rcond=None)[0]

    def _update_statistics(self, solve_time: float, matrix_size: int):
        """更新求解统计"""
        self.solve_count += 1
        self.total_solve_time += solve_time


class MultiLevelLinearSolver(LinearSolverBase):
    """
    多层线性求解器（基于adevice_complement4.md规范）

    使用多重网格方法求解大规模线性方程组
    """

    def __init__(self, config: Dict):
        """
        初始化多层线性求解器

        Args:
            config: 求解器配置
        """
        super().__init__(config)

        self.n_levels = config.get('n_levels', 3)
        self.coarsening_factor = config.get('coarsening_factor', 2)
        self.pre_smooth_iterations = config.get('pre_smooth_iterations', 2)
        self.post_smooth_iterations = config.get('post_smooth_iterations', 2)
        self.coarse_solver_tolerance = config.get('coarse_solver_tolerance', 1e-8)

        print(f"✅ 多层线性求解器初始化完成")
        print(f"   层数: {self.n_levels}")
        print(f"   粗化因子: {self.coarsening_factor}")
        print(f"   预平滑: {self.pre_smooth_iterations} 次")
        print(f"   后平滑: {self.post_smooth_iterations} 次")

    def solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """
        多层求解线性方程组

        Args:
            A: 系数矩阵 [N, N]
            b: 右端向量 [N]

        Returns:
            x: 解向量 [N]
        """
        import time
        start_time = time.time()

        try:
            n = A.shape[0]

            # 如果矩阵较小，直接求解
            if n <= 100:
                return self._direct_solve(A, b)

            # 构建多层结构
            levels = self._build_multilevel_structure(A, b)

            # V-cycle求解
            x = np.zeros(n)
            for iteration in range(self.max_iterations):
                x_old = x.copy()
                x = self._v_cycle(levels, x, 0)

                # 检查收敛性
                residual = np.linalg.norm(A @ x - b)
                relative_residual = residual / (np.linalg.norm(b) + 1e-12)

                if relative_residual < self.tolerance:
                    print(f"   多层方法在 {iteration + 1} 次迭代后收敛")
                    break

            solve_time = time.time() - start_time
            self._update_statistics(solve_time, n)

            return x

        except Exception as e:
            print(f"   ❌ 多层求解失败: {e}")
            # 回退到直接求解
            return self._direct_solve(A, b)

    def _build_multilevel_structure(self, A: np.ndarray, b: np.ndarray) -> list:
        """构建多层结构"""
        levels = []
        current_A = A.copy()
        current_b = b.copy()

        for level in range(self.n_levels):
            level_data = {
                'A': current_A,
                'b': current_b,
                'size': current_A.shape[0]
            }
            levels.append(level_data)

            # 如果已经足够小，停止粗化
            if current_A.shape[0] <= 50:
                break

            # 构建限制和插值算子
            restriction_op, interpolation_op = self._build_transfer_operators(current_A)

            # 粗化矩阵和右端项
            current_A = restriction_op @ current_A @ interpolation_op
            current_b = restriction_op @ current_b

            level_data['restriction'] = restriction_op
            level_data['interpolation'] = interpolation_op

        return levels

    def _build_transfer_operators(self, A: np.ndarray) -> tuple:
        """构建传递算子"""
        n = A.shape[0]
        n_coarse = n // self.coarsening_factor

        # 简化的线性插值
        interpolation = np.zeros((n, n_coarse))
        restriction = np.zeros((n_coarse, n))

        for i in range(n_coarse):
            fine_start = i * self.coarsening_factor
            fine_end = min((i + 1) * self.coarsening_factor, n)

            # 插值：粗网格点到细网格点
            for j in range(fine_start, fine_end):
                interpolation[j, i] = 1.0

            # 限制：细网格点到粗网格点（平均）
            restriction[i, fine_start:fine_end] = 1.0 / (fine_end - fine_start)

        return restriction, interpolation

    def _v_cycle(self, levels: list, x: np.ndarray, level: int) -> np.ndarray:
        """V-cycle多重网格"""
        if level >= len(levels) - 1:
            # 最粗层：直接求解
            A_coarse = levels[level]['A']
            b_coarse = levels[level]['b']
            return self._direct_solve(A_coarse, b_coarse)

        A = levels[level]['A']
        b = levels[level]['b']

        # 预平滑
        x = self._smooth(A, b, x, self.pre_smooth_iterations)

        # 计算残差
        residual = b - A @ x

        # 限制到粗层
        restriction = levels[level]['restriction']
        coarse_residual = restriction @ residual

        # 粗层求解
        coarse_correction = self._v_cycle(levels, np.zeros(len(coarse_residual)), level + 1)

        # 插值回细层
        interpolation = levels[level]['interpolation']
        correction = interpolation @ coarse_correction

        # 应用修正
        x = x + correction

        # 后平滑
        x = self._smooth(A, b, x, self.post_smooth_iterations)

        return x

    def _smooth(self, A: np.ndarray, b: np.ndarray, x: np.ndarray, iterations: int) -> np.ndarray:
        """平滑迭代（Gauss-Seidel）"""
        n = A.shape[0]

        for _ in range(iterations):
            for i in range(n):
                if abs(A[i, i]) > 1e-12:
                    sum_ax = np.sum(A[i, :] * x) - A[i, i] * x[i]
                    x[i] = (b[i] - sum_ax) / A[i, i]

        return x

    def _direct_solve(self, A: np.ndarray, b: np.ndarray) -> np.ndarray:
        """直接求解"""
        try:
            if SCIPY_AVAILABLE:
                return solve(A, b)
            else:
                return np.linalg.solve(A, b)
        except Exception:
            return np.linalg.lstsq(A, b, rcond=None)[0]

    def _update_statistics(self, solve_time: float, matrix_size: int):
        """更新求解统计"""
        self.solve_count += 1
        self.total_solve_time += solve_time


# 增强的工厂函数

def create_enhanced_linear_solver(solver_type: str, config: Dict) -> LinearSolverBase:
    """
    创建增强的线性求解器

    Args:
        solver_type: 求解器类型 ('gpu', 'block', 'multilevel', 'adaptive')
        config: 求解器配置

    Returns:
        LinearSolverBase: 线性求解器实例
    """
    if solver_type == 'gpu':
        return GPULinearSolver(config)
    elif solver_type == 'block':
        return BlockLinearSolver(config)
    elif solver_type == 'multilevel':
        return MultiLevelLinearSolver(config)
    elif solver_type == 'adaptive':
        return AdaptiveLinearSolver(config)
    elif solver_type == 'direct':
        return DirectLinearSolver(config)
    elif solver_type == 'iterative':
        return IterativeLinearSolver(config)
    else:
        raise ValueError(f"不支持的增强求解器类型: {solver_type}")

def create_optimal_linear_solver(matrix_properties: Dict, config: Optional[Dict] = None) -> LinearSolverBase:
    """
    根据矩阵特性创建最优线性求解器

    Args:
        matrix_properties: 矩阵特性字典
        config: 求解器配置

    Returns:
        LinearSolverBase: 最优线性求解器实例
    """
    if config is None:
        config = {'tolerance': 1e-6, 'max_iterations': 1000}

    matrix_size = matrix_properties.get('size', 1000)
    condition_number = matrix_properties.get('condition_number', 1e6)
    sparsity = matrix_properties.get('sparsity', 0.1)
    is_symmetric = matrix_properties.get('is_symmetric', False)
    is_positive_definite = matrix_properties.get('is_positive_definite', False)

    # 决策逻辑
    if matrix_size > 10000 and sparsity > 0.8:
        # 大规模稀疏矩阵：使用多层求解器
        print("   选择多层求解器（大规模稀疏矩阵）")
        return MultiLevelLinearSolver(config)

    elif matrix_size > 5000:
        # 大规模矩阵：使用分块求解器
        print("   选择分块求解器（大规模矩阵）")
        return BlockLinearSolver(config)

    elif matrix_size > 1000 and condition_number < 1e8:
        # 中等规模良态矩阵：使用GPU求解器
        print("   选择GPU求解器（中等规模良态矩阵）")
        return GPULinearSolver(config)

    elif condition_number > 1e10:
        # 病态矩阵：使用迭代求解器
        print("   选择迭代求解器（病态矩阵）")
        config['method'] = 'gmres'
        config['use_preconditioner'] = True
        return IterativeLinearSolver(config)

    else:
        # 默认：使用自适应求解器
        print("   选择自适应求解器（默认选择）")
        return AdaptiveLinearSolver(config)
