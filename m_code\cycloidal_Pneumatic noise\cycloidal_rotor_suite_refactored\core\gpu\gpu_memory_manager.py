"""
GPU内存管理器
============

提供智能的GPU内存管理和优化

基于adevice_complement4.md技术规范实现
"""

import numpy as np
from typing import Optional, Dict, List, Any, Tuple
import warnings
import gc

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False


class GPUMemoryManager:
    """
    GPU内存管理器
    
    提供智能的GPU内存分配、释放和优化功能
    """
    
    def __init__(self, backend: str = "auto", device_id: int = 0):
        """
        初始化GPU内存管理器
        
        Args:
            backend: 计算后端 ("torch", "cupy", "auto")
            device_id: GPU设备ID
        """
        self.backend = backend
        self.device_id = device_id
        self.device = None
        self.is_available = False
        
        # 内存使用统计
        self.allocated_tensors = {}
        self.peak_memory_usage = 0
        self.current_memory_usage = 0
        
        self._initialize_backend()
    
    def _initialize_backend(self):
        """初始化计算后端"""
        if self.backend == "auto":
            if TORCH_AVAILABLE:
                self.backend = "torch"
            elif CUPY_AVAILABLE:
                self.backend = "cupy"
            else:
                self.backend = "cpu"
                warnings.warn("GPU后端不可用，内存管理器将被禁用")
        
        if self.backend == "torch" and TORCH_AVAILABLE:
            try:
                if torch.cuda.is_available():
                    self.device = torch.device(f"cuda:{self.device_id}")
                    self.is_available = True
                    # 清空缓存
                    torch.cuda.empty_cache()
                else:
                    self.backend = "cpu"
                    warnings.warn("CUDA不可用，内存管理器将被禁用")
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"Torch初始化失败: {e}")
        
        elif self.backend == "cupy" and CUPY_AVAILABLE:
            try:
                cp.cuda.Device(self.device_id).use()
                self.is_available = True
                # 清空内存池
                mempool = cp.get_default_memory_pool()
                mempool.free_all_blocks()
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"CuPy初始化失败: {e}")
    
    def allocate_tensor(self, shape: Tuple[int, ...], dtype: str = "float32", name: str = None) -> Any:
        """
        分配GPU张量
        
        Args:
            shape: 张量形状
            dtype: 数据类型
            name: 张量名称（用于跟踪）
            
        Returns:
            tensor: GPU张量
        """
        if not self.is_available:
            return np.zeros(shape, dtype=getattr(np, dtype))
        
        try:
            if self.backend == "torch":
                tensor = torch.zeros(shape, dtype=getattr(torch, dtype), device=self.device)
                
                # 记录分配的张量
                if name:
                    self.allocated_tensors[name] = {
                        'tensor': tensor,
                        'shape': shape,
                        'dtype': dtype,
                        'size_bytes': tensor.numel() * tensor.element_size()
                    }
                
                self._update_memory_stats()
                return tensor
                
            elif self.backend == "cupy":
                tensor = cp.zeros(shape, dtype=getattr(cp, dtype))
                
                # 记录分配的张量
                if name:
                    self.allocated_tensors[name] = {
                        'tensor': tensor,
                        'shape': shape,
                        'dtype': dtype,
                        'size_bytes': tensor.nbytes
                    }
                
                self._update_memory_stats()
                return tensor
                
        except Exception as e:
            warnings.warn(f"GPU张量分配失败: {e}")
            return np.zeros(shape, dtype=getattr(np, dtype))
    
    def deallocate_tensor(self, name: str):
        """
        释放指定的GPU张量
        
        Args:
            name: 张量名称
        """
        if not self.is_available or name not in self.allocated_tensors:
            return
        
        try:
            tensor_info = self.allocated_tensors[name]
            
            if self.backend == "torch":
                # PyTorch会自动管理内存，只需删除引用
                del tensor_info['tensor']
                
            elif self.backend == "cupy":
                # CuPy也会自动管理内存
                del tensor_info['tensor']
            
            # 从记录中删除
            del self.allocated_tensors[name]
            
            self._update_memory_stats()
            
        except Exception as e:
            warnings.warn(f"张量释放失败: {e}")
    
    def clear_cache(self):
        """清空GPU缓存"""
        if not self.is_available:
            return
        
        try:
            if self.backend == "torch":
                torch.cuda.empty_cache()
                
            elif self.backend == "cupy":
                mempool = cp.get_default_memory_pool()
                mempool.free_all_blocks()
            
            # 清空记录
            self.allocated_tensors.clear()
            self._update_memory_stats()
            
        except Exception as e:
            warnings.warn(f"缓存清理失败: {e}")
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        if not self.is_available:
            return
        
        try:
            # 强制垃圾回收
            gc.collect()
            
            if self.backend == "torch":
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                
            elif self.backend == "cupy":
                mempool = cp.get_default_memory_pool()
                mempool.free_all_blocks()
                cp.cuda.Stream.null.synchronize()
            
            self._update_memory_stats()
            
        except Exception as e:
            warnings.warn(f"内存优化失败: {e}")
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        获取内存使用信息
        
        Returns:
            memory_info: 内存信息字典
        """
        if not self.is_available:
            return {
                'backend': 'cpu',
                'total_memory': 0,
                'allocated_memory': 0,
                'cached_memory': 0,
                'free_memory': 0,
                'peak_memory': 0,
                'allocated_tensors': 0
            }
        
        try:
            if self.backend == "torch":
                total_memory = torch.cuda.get_device_properties(self.device).total_memory
                allocated_memory = torch.cuda.memory_allocated(self.device)
                cached_memory = torch.cuda.memory_reserved(self.device)
                free_memory = total_memory - cached_memory
                peak_memory = torch.cuda.max_memory_allocated(self.device)
                
                return {
                    'backend': 'torch',
                    'device': str(self.device),
                    'total_memory': total_memory,
                    'allocated_memory': allocated_memory,
                    'cached_memory': cached_memory,
                    'free_memory': free_memory,
                    'peak_memory': peak_memory,
                    'allocated_tensors': len(self.allocated_tensors)
                }
                
            elif self.backend == "cupy":
                mempool = cp.get_default_memory_pool()
                total_memory = mempool.total_bytes()
                used_memory = mempool.used_bytes()
                free_memory = total_memory - used_memory
                
                return {
                    'backend': 'cupy',
                    'device': f'cuda:{self.device_id}',
                    'total_memory': total_memory,
                    'allocated_memory': used_memory,
                    'cached_memory': used_memory,
                    'free_memory': free_memory,
                    'peak_memory': self.peak_memory_usage,
                    'allocated_tensors': len(self.allocated_tensors)
                }
                
        except Exception as e:
            warnings.warn(f"获取内存信息失败: {e}")
            
        return {
            'backend': self.backend,
            'error': 'Failed to get memory info'
        }
    
    def _update_memory_stats(self):
        """更新内存使用统计"""
        try:
            if self.backend == "torch":
                current_memory = torch.cuda.memory_allocated(self.device)
                peak_memory = torch.cuda.max_memory_allocated(self.device)
                
            elif self.backend == "cupy":
                mempool = cp.get_default_memory_pool()
                current_memory = mempool.used_bytes()
                peak_memory = max(self.peak_memory_usage, current_memory)
                
            else:
                return
            
            self.current_memory_usage = current_memory
            self.peak_memory_usage = max(self.peak_memory_usage, peak_memory)
            
        except Exception:
            pass
    
    def check_memory_availability(self, required_bytes: int) -> bool:
        """
        检查是否有足够的内存
        
        Args:
            required_bytes: 需要的内存字节数
            
        Returns:
            available: 是否有足够内存
        """
        if not self.is_available:
            return True  # CPU内存假设总是足够的
        
        try:
            memory_info = self.get_memory_info()
            free_memory = memory_info.get('free_memory', 0)
            
            return free_memory >= required_bytes
            
        except Exception:
            return False
    
    def suggest_batch_size(self, single_item_memory: int, max_memory_fraction: float = 0.8) -> int:
        """
        建议批处理大小
        
        Args:
            single_item_memory: 单个项目的内存需求
            max_memory_fraction: 最大内存使用比例
            
        Returns:
            batch_size: 建议的批处理大小
        """
        if not self.is_available or single_item_memory <= 0:
            return 1
        
        try:
            memory_info = self.get_memory_info()
            available_memory = memory_info.get('free_memory', 0)
            max_usable_memory = int(available_memory * max_memory_fraction)
            
            suggested_batch_size = max(1, max_usable_memory // single_item_memory)
            
            return suggested_batch_size
            
        except Exception:
            return 1
    
    def get_tensor_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定张量的信息
        
        Args:
            name: 张量名称
            
        Returns:
            tensor_info: 张量信息
        """
        return self.allocated_tensors.get(name, None)
    
    def list_allocated_tensors(self) -> List[str]:
        """
        列出所有已分配的张量名称
        
        Returns:
            tensor_names: 张量名称列表
        """
        return list(self.allocated_tensors.keys())


def create_gpu_memory_manager(backend: str = "auto", device_id: int = 0) -> GPUMemoryManager:
    """
    创建GPU内存管理器的工厂函数
    
    Args:
        backend: 计算后端
        device_id: GPU设备ID
        
    Returns:
        GPUMemoryManager实例
    """
    return GPUMemoryManager(backend=backend, device_id=device_id)
