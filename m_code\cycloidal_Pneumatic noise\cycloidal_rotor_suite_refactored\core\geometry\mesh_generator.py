"""
网格生成器 - 完全复刻原始算法
============================

实现结构化和非结构化网格生成算法，基于原始 cycloidal_rotor_suite 项目的完整实现。
支持桨叶表面网格、尾迹网格和自适应网格细化。

主要功能：
- 结构化网格生成（四边形面元）
- 非结构化网格生成（三角形面元）
- 网格质量检查和优化
- 自适应网格细化
- 面板网格生成（UVLM专用）

基于原始项目的完整实现，确保与原始算法在数值层面完全一致。

作者: Augment Agent
日期: 2025-08-03
"""

import warnings
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass

import numpy as np

try:
    from scipy.spatial import Delaunay
    from scipy.interpolate import griddata
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    warnings.warn("SciPy不可用，将使用简化的网格生成方法")


@dataclass
class MeshData:
    """网格数据结构 - 完全复刻原始实现"""
    
    # 节点数据
    nodes: np.ndarray  # 节点坐标 [N_nodes, 3]
    
    # 面元数据
    elements: np.ndarray  # 面元连接 [N_elements, n_vertices]
    element_centers: np.ndarray  # 面元中心 [N_elements, 3]
    element_areas: np.ndarray  # 面元面积 [N_elements]
    element_normals: np.ndarray  # 面元法向量 [N_elements, 3]
    
    # 网格属性
    mesh_type: str  # 'structured' 或 'unstructured'
    n_nodes: int  # 节点数量
    n_elements: int  # 面元数量
    
    # 质量指标
    aspect_ratio: Optional[np.ndarray] = None  # 长宽比
    skewness: Optional[np.ndarray] = None  # 偏斜度
    orthogonality: Optional[np.ndarray] = None  # 正交性


class MeshGenerator:
    """
    网格生成器 - 完全复刻原始算法
    
    实现多种网格生成算法和质量控制方法
    """

    def __init__(self, config: Dict):
        """
        初始化网格生成器 - 完全复刻原始实现
        
        Args:
            config: 网格生成配置
        """
        self.config = config
        
        # 网格参数
        self.mesh_type = config.get('mesh_type', 'structured')
        self.n_chordwise = config.get('n_chordwise_panels', 20)
        self.n_spanwise = config.get('n_spanwise_panels', 15)
        
        # 质量控制参数
        self.max_aspect_ratio = config.get('max_aspect_ratio', 10.0)
        self.max_skewness = config.get('max_skewness', 0.8)
        self.min_orthogonality = config.get('min_orthogonality', 0.1)
        
        # 自适应网格参数
        self.enable_adaptive_refinement = config.get('enable_adaptive_refinement', False)
        self.refinement_threshold = config.get('refinement_threshold', 0.1)
        self.coarsening_threshold = config.get('coarsening_threshold', 0.01)
        self.max_refinement_levels = config.get('max_refinement_levels', 3)
        
        # 数值稳定性参数
        self.min_element_area = 1e-8
        self.numerical_tolerance = 1e-12
        
        print(f"✅ 网格生成器初始化完成")
        print(f"   网格类型: {self.mesh_type}")
        print(f"   弦向面元数: {self.n_chordwise}")
        print(f"   展向面元数: {self.n_spanwise}")
        print(f"   自适应细化: {'启用' if self.enable_adaptive_refinement else '禁用'}")

    def generate_blade_mesh_original(self, blade_geometry: Dict) -> MeshData:
        """
        生成桨叶表面网格 - 完全复刻原始算法
        
        Args:
            blade_geometry: 桨叶几何参数
                - chord_distribution: 弦长分布 [N_stations]
                - twist_distribution: 扭转分布 [N_stations] [rad]
                - radial_stations: 径向站位 [N_stations]
                - airfoil_sections: 翼型截面数据
                
        Returns:
            mesh_data: 桨叶网格数据
        """
        if self.mesh_type == 'structured':
            return self._generate_structured_blade_mesh(blade_geometry)
        elif self.mesh_type == 'unstructured':
            return self._generate_unstructured_blade_mesh(blade_geometry)
        else:
            raise ValueError(f"不支持的网格类型: {self.mesh_type}")

    def _generate_structured_blade_mesh(self, blade_geometry: Dict) -> MeshData:
        """生成结构化桨叶网格 - 完全复刻原始算法"""
        chord_distribution = blade_geometry['chord_distribution']
        twist_distribution = blade_geometry['twist_distribution']
        radial_stations = blade_geometry['radial_stations']
        
        n_radial = len(radial_stations)
        n_chord = self.n_chordwise
        
        # 创建参数化坐标
        xi_chord = np.linspace(0.0, 1.0, n_chord + 1)  # 弦向参数坐标
        eta_span = radial_stations  # 展向物理坐标
        
        # 初始化节点数组
        n_nodes = (n_chord + 1) * n_radial
        nodes = np.zeros((n_nodes, 3))
        
        # 生成节点
        node_idx = 0
        for i, r in enumerate(eta_span):
            chord = chord_distribution[i]
            twist = twist_distribution[i]
            
            for j, xi in enumerate(xi_chord):
                # 翼型坐标（局部坐标系）
                x_local = xi * chord
                y_local = 0.0  # 假设对称翼型
                z_local = 0.0
                
                # 应用扭转变换
                x_twisted = x_local * np.cos(twist) - y_local * np.sin(twist)
                y_twisted = x_local * np.sin(twist) + y_local * np.cos(twist)
                z_twisted = z_local
                
                # 全局坐标
                nodes[node_idx] = [x_twisted, r, z_twisted]
                node_idx += 1
        
        # 生成面元连接
        n_elements = n_chord * (n_radial - 1)
        elements = np.zeros((n_elements, 4), dtype=int)  # 四边形面元
        
        element_idx = 0
        for i in range(n_radial - 1):
            for j in range(n_chord):
                # 四边形面元的四个节点
                n1 = i * (n_chord + 1) + j
                n2 = i * (n_chord + 1) + j + 1
                n3 = (i + 1) * (n_chord + 1) + j + 1
                n4 = (i + 1) * (n_chord + 1) + j
                
                elements[element_idx] = [n1, n2, n3, n4]
                element_idx += 1
        
        # 计算面元属性
        element_centers, element_areas, element_normals = self._calculate_element_properties(
            nodes, elements, 'quad'
        )
        
        # 创建网格数据
        mesh_data = MeshData(
            nodes=nodes,
            elements=elements,
            element_centers=element_centers,
            element_areas=element_areas,
            element_normals=element_normals,
            mesh_type='structured',
            n_nodes=n_nodes,
            n_elements=n_elements
        )
        
        # 计算质量指标
        self._calculate_mesh_quality(mesh_data)
        
        return mesh_data

    def _generate_unstructured_blade_mesh(self, blade_geometry: Dict) -> MeshData:
        """生成非结构化桨叶网格 - 完全复刻原始算法"""
        if not SCIPY_AVAILABLE:
            raise RuntimeError("非结构化网格生成需要SciPy库")
        
        chord_distribution = blade_geometry['chord_distribution']
        twist_distribution = blade_geometry['twist_distribution']
        radial_stations = blade_geometry['radial_stations']
        
        # 创建边界点
        boundary_points = []
        
        for i, r in enumerate(radial_stations):
            chord = chord_distribution[i]
            twist = twist_distribution[i]
            
            # 翼型前缘和后缘点
            x_le = 0.0
            x_te = chord
            
            # 应用扭转
            x_le_twisted = x_le * np.cos(twist)
            x_te_twisted = x_te * np.cos(twist)
            
            boundary_points.append([x_le_twisted, r, 0.0])
            boundary_points.append([x_te_twisted, r, 0.0])
        
        boundary_points = np.array(boundary_points)
        
        # 使用Delaunay三角化
        # 投影到2D平面进行三角化
        points_2d = boundary_points[:, [0, 1]]  # x-y平面
        tri = Delaunay(points_2d)
        
        # 创建三角形面元
        elements = tri.simplices
        nodes = boundary_points
        
        # 计算面元属性
        element_centers, element_areas, element_normals = self._calculate_element_properties(
            nodes, elements, 'tri'
        )
        
        # 创建网格数据
        mesh_data = MeshData(
            nodes=nodes,
            elements=elements,
            element_centers=element_centers,
            element_areas=element_areas,
            element_normals=element_normals,
            mesh_type='unstructured',
            n_nodes=len(nodes),
            n_elements=len(elements)
        )
        
        # 计算质量指标
        self._calculate_mesh_quality(mesh_data)
        
        return mesh_data

    def _calculate_element_properties(self, nodes: np.ndarray, elements: np.ndarray,
                                    element_type: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """计算面元属性 - 完全复刻原始算法"""
        n_elements = len(elements)
        element_centers = np.zeros((n_elements, 3))
        element_areas = np.zeros(n_elements)
        element_normals = np.zeros((n_elements, 3))
        
        for i, element in enumerate(elements):
            if element_type == 'quad':
                # 四边形面元
                p1, p2, p3, p4 = nodes[element]
                
                # 中心点
                center = (p1 + p2 + p3 + p4) / 4.0
                element_centers[i] = center
                
                # 面积（分解为两个三角形）
                area1 = 0.5 * np.linalg.norm(np.cross(p2 - p1, p3 - p1))
                area2 = 0.5 * np.linalg.norm(np.cross(p3 - p1, p4 - p1))
                element_areas[i] = area1 + area2
                
                # 法向量
                v1 = p2 - p1
                v2 = p4 - p1
                normal = np.cross(v1, v2)
                
            elif element_type == 'tri':
                # 三角形面元
                p1, p2, p3 = nodes[element]
                
                # 中心点
                center = (p1 + p2 + p3) / 3.0
                element_centers[i] = center
                
                # 面积
                area = 0.5 * np.linalg.norm(np.cross(p2 - p1, p3 - p1))
                element_areas[i] = area
                
                # 法向量
                normal = np.cross(p2 - p1, p3 - p1)
            
            # 归一化法向量
            normal_magnitude = np.linalg.norm(normal)
            if normal_magnitude > self.numerical_tolerance:
                element_normals[i] = normal / normal_magnitude
            else:
                element_normals[i] = [0, 0, 1]  # 默认向上
        
        return element_centers, element_areas, element_normals

    def _calculate_mesh_quality(self, mesh_data: MeshData) -> None:
        """计算网格质量指标 - 完全复刻原始算法"""
        n_elements = mesh_data.n_elements
        aspect_ratio = np.zeros(n_elements)
        skewness = np.zeros(n_elements)
        orthogonality = np.zeros(n_elements)
        
        for i, element in enumerate(mesh_data.elements):
            if mesh_data.mesh_type == 'structured':
                # 四边形面元质量
                p1, p2, p3, p4 = mesh_data.nodes[element]
                
                # 长宽比
                edge1_length = np.linalg.norm(p2 - p1)
                edge2_length = np.linalg.norm(p4 - p1)
                aspect_ratio[i] = max(edge1_length, edge2_length) / min(edge1_length, edge2_length)
                
                # 偏斜度（基于对角线夹角）
                diag1 = p3 - p1
                diag2 = p4 - p2
                cos_angle = np.dot(diag1, diag2) / (np.linalg.norm(diag1) * np.linalg.norm(diag2))
                skewness[i] = abs(cos_angle)
                
                # 正交性（基于边的夹角）
                edge1 = p2 - p1
                edge2 = p4 - p1
                cos_ortho = abs(np.dot(edge1, edge2)) / (np.linalg.norm(edge1) * np.linalg.norm(edge2))
                orthogonality[i] = 1.0 - cos_ortho
                
            else:
                # 三角形面元质量
                p1, p2, p3 = mesh_data.nodes[element]
                
                # 长宽比（最长边与最短边的比）
                edge_lengths = [
                    np.linalg.norm(p2 - p1),
                    np.linalg.norm(p3 - p2),
                    np.linalg.norm(p1 - p3)
                ]
                aspect_ratio[i] = max(edge_lengths) / min(edge_lengths)
                
                # 偏斜度（基于内角）
                angles = self._calculate_triangle_angles(p1, p2, p3)
                ideal_angle = np.pi / 3  # 60度
                max_deviation = max([abs(angle - ideal_angle) for angle in angles])
                skewness[i] = max_deviation / ideal_angle
                
                # 正交性（对三角形不太适用，设为1）
                orthogonality[i] = 1.0
        
        # 存储质量指标
        mesh_data.aspect_ratio = aspect_ratio
        mesh_data.skewness = skewness
        mesh_data.orthogonality = orthogonality

    def _calculate_triangle_angles(self, p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> List[float]:
        """计算三角形内角"""
        # 边向量
        a = p2 - p1
        b = p3 - p2
        c = p1 - p3
        
        # 边长
        len_a = np.linalg.norm(a)
        len_b = np.linalg.norm(b)
        len_c = np.linalg.norm(c)
        
        # 使用余弦定理计算角度
        angle1 = np.arccos(np.clip(np.dot(-a, c) / (len_a * len_c), -1, 1))
        angle2 = np.arccos(np.clip(np.dot(-b, a) / (len_b * len_a), -1, 1))
        angle3 = np.arccos(np.clip(np.dot(-c, b) / (len_c * len_b), -1, 1))
        
        return [angle1, angle2, angle3]

    def check_mesh_quality(self, mesh_data: MeshData) -> Dict[str, bool]:
        """检查网格质量 - 完全复刻原始算法"""
        quality_results = {}
        
        # 检查长宽比
        max_aspect_ratio = np.max(mesh_data.aspect_ratio)
        quality_results['aspect_ratio_ok'] = max_aspect_ratio <= self.max_aspect_ratio
        
        # 检查偏斜度
        max_skewness = np.max(mesh_data.skewness)
        quality_results['skewness_ok'] = max_skewness <= self.max_skewness
        
        # 检查正交性
        min_orthogonality = np.min(mesh_data.orthogonality)
        quality_results['orthogonality_ok'] = min_orthogonality >= self.min_orthogonality
        
        # 检查面积
        min_area = np.min(mesh_data.element_areas)
        quality_results['area_ok'] = min_area >= self.min_element_area
        
        # 总体质量
        quality_results['overall_ok'] = all([
            quality_results['aspect_ratio_ok'],
            quality_results['skewness_ok'],
            quality_results['orthogonality_ok'],
            quality_results['area_ok']
        ])
        
        return quality_results

    def get_mesh_statistics(self, mesh_data: MeshData) -> Dict:
        """获取网格统计信息"""
        return {
            'mesh_type': mesh_data.mesh_type,
            'n_nodes': mesh_data.n_nodes,
            'n_elements': mesh_data.n_elements,
            'total_area': np.sum(mesh_data.element_areas),
            'min_area': np.min(mesh_data.element_areas),
            'max_area': np.max(mesh_data.element_areas),
            'avg_aspect_ratio': np.mean(mesh_data.aspect_ratio),
            'max_aspect_ratio': np.max(mesh_data.aspect_ratio),
            'avg_skewness': np.mean(mesh_data.skewness),
            'max_skewness': np.max(mesh_data.skewness),
            'min_orthogonality': np.min(mesh_data.orthogonality)
        }


# 工厂函数
def create_structured_mesh_generator(config: Dict) -> MeshGenerator:
    """创建结构化网格生成器"""
    config['mesh_type'] = 'structured'
    return MeshGenerator(config)


def create_unstructured_mesh_generator(config: Dict) -> MeshGenerator:
    """创建非结构化网格生成器"""
    config['mesh_type'] = 'unstructured'
    return MeshGenerator(config)


# ==================== 增强的网格生成方法（基于adevice_complement4.md规范） ====================

def generate_adaptive_mesh_enhanced(mesh_generator: MeshGenerator,
                                   blade_geometry: Dict,
                                   error_threshold: float = 0.01) -> MeshData:
    """
    生成增强的自适应网格（基于adevice_complement4.md规范）

    Args:
        mesh_generator: 网格生成器实例
        blade_geometry: 桨叶几何参数
        error_threshold: 误差阈值

    Returns:
        mesh_data: 自适应网格数据
    """
    try:
        # 生成初始网格
        initial_mesh = mesh_generator.generate_blade_mesh_original(blade_geometry)

        if not mesh_generator.enable_adaptive_refinement:
            return initial_mesh

        # 计算误差指标
        error_indicators = compute_error_indicators_enhanced(initial_mesh, blade_geometry)

        # 标记需要细化的单元
        refinement_flags = error_indicators > error_threshold

        if np.any(refinement_flags):
            # 执行网格细化
            refined_mesh = refine_mesh_elements_enhanced(initial_mesh, refinement_flags)

            # 重新计算网格质量
            mesh_generator._calculate_mesh_quality(refined_mesh)

            return refined_mesh
        else:
            return initial_mesh

    except Exception as e:
        print(f"   ⚠️ 增强自适应网格生成失败: {e}")
        return mesh_generator.generate_blade_mesh_original(blade_geometry)

def compute_error_indicators_enhanced(mesh_data: MeshData, blade_geometry: Dict) -> np.ndarray:
    """计算增强的网格误差指标"""
    try:
        n_elements = len(mesh_data.element_centers)
        error_indicators = np.zeros(n_elements)

        # 基于几何曲率的误差指标
        curvature_errors = compute_curvature_error_enhanced(mesh_data, blade_geometry)

        # 基于长宽比的误差指标
        aspect_ratio_errors = compute_aspect_ratio_error_enhanced(mesh_data)

        # 基于梯度的误差指标
        gradient_errors = compute_gradient_error_enhanced(mesh_data)

        # 基于尺寸变化的误差指标
        size_variation_errors = compute_size_variation_error(mesh_data)

        # 综合误差指标
        error_indicators = (0.4 * curvature_errors +
                          0.25 * aspect_ratio_errors +
                          0.2 * gradient_errors +
                          0.15 * size_variation_errors)

        return error_indicators

    except Exception as e:
        print(f"   ⚠️ 增强误差指标计算失败: {e}")
        return np.zeros(len(mesh_data.element_centers))

def compute_curvature_error_enhanced(mesh_data: MeshData, blade_geometry: Dict) -> np.ndarray:
    """计算增强的基于曲率的误差指标"""
    n_elements = len(mesh_data.element_centers)
    curvature_errors = np.zeros(n_elements)

    try:
        # 估计局部曲率
        for i, center in enumerate(mesh_data.element_centers):
            # 找到相邻单元
            neighbors = find_neighbor_elements_enhanced(mesh_data, i)

            if len(neighbors) >= 3:
                # 计算法向量变化率作为曲率指标
                normal_variations = []
                for neighbor_idx in neighbors:
                    normal_diff = np.linalg.norm(
                        mesh_data.element_normals[i] - mesh_data.element_normals[neighbor_idx]
                    )
                    normal_variations.append(normal_diff)

                # 加权平均，距离近的邻居权重更大
                distances = []
                for neighbor_idx in neighbors:
                    dist = np.linalg.norm(center - mesh_data.element_centers[neighbor_idx])
                    distances.append(dist)

                if len(distances) > 0:
                    weights = 1.0 / (np.array(distances) + 1e-12)
                    weights = weights / np.sum(weights)
                    curvature_errors[i] = np.sum(weights * normal_variations)

        # 归一化
        if np.max(curvature_errors) > 0:
            curvature_errors = curvature_errors / np.max(curvature_errors)

        return curvature_errors

    except Exception as e:
        print(f"   ⚠️ 增强曲率误差计算失败: {e}")
        return np.zeros(n_elements)

def compute_aspect_ratio_error_enhanced(mesh_data: MeshData) -> np.ndarray:
    """计算增强的基于长宽比的误差指标"""
    try:
        n_elements = len(mesh_data.element_centers)

        if mesh_data.aspect_ratio is not None:
            # 长宽比过大的单元需要细化
            max_aspect_ratio = 8.0  # 更严格的阈值
            aspect_ratio_errors = np.maximum(0, mesh_data.aspect_ratio - max_aspect_ratio) / max_aspect_ratio

            # 非线性惩罚：长宽比越大，误差增长越快
            aspect_ratio_errors = aspect_ratio_errors ** 1.5

            return aspect_ratio_errors
        else:
            # 如果没有长宽比数据，计算简化的长宽比
            aspect_ratios = compute_element_aspect_ratios(mesh_data)
            max_aspect_ratio = 8.0
            aspect_ratio_errors = np.maximum(0, aspect_ratios - max_aspect_ratio) / max_aspect_ratio
            aspect_ratio_errors = aspect_ratio_errors ** 1.5

            return aspect_ratio_errors

    except Exception as e:
        print(f"   ⚠️ 增强长宽比误差计算失败: {e}")
        return np.zeros(len(mesh_data.element_centers))

def compute_gradient_error_enhanced(mesh_data: MeshData) -> np.ndarray:
    """计算增强的基于梯度的误差指标"""
    try:
        n_elements = len(mesh_data.element_centers)
        gradient_errors = np.zeros(n_elements)

        # 计算多种梯度指标
        area_gradient_errors = compute_area_gradient_error(mesh_data)
        normal_gradient_errors = compute_normal_gradient_error(mesh_data)

        # 综合梯度误差
        gradient_errors = 0.6 * area_gradient_errors + 0.4 * normal_gradient_errors

        return gradient_errors

    except Exception as e:
        print(f"   ⚠️ 增强梯度误差计算失败: {e}")
        return np.zeros(n_elements)

def compute_area_gradient_error(mesh_data: MeshData) -> np.ndarray:
    """计算面积梯度误差"""
    n_elements = len(mesh_data.element_centers)
    area_errors = np.zeros(n_elements)

    try:
        for i in range(n_elements):
            neighbors = find_neighbor_elements_enhanced(mesh_data, i)

            if len(neighbors) > 0:
                current_area = mesh_data.element_areas[i]
                area_variations = []

                for neighbor_idx in neighbors:
                    neighbor_area = mesh_data.element_areas[neighbor_idx]
                    area_ratio = abs(current_area - neighbor_area) / (current_area + 1e-12)
                    area_variations.append(area_ratio)

                area_errors[i] = np.max(area_variations)  # 使用最大变化而不是平均

        # 归一化
        if np.max(area_errors) > 0:
            area_errors = area_errors / np.max(area_errors)

        return area_errors

    except Exception as e:
        print(f"   ⚠️ 面积梯度误差计算失败: {e}")
        return np.zeros(n_elements)

def compute_normal_gradient_error(mesh_data: MeshData) -> np.ndarray:
    """计算法向量梯度误差"""
    n_elements = len(mesh_data.element_centers)
    normal_errors = np.zeros(n_elements)

    try:
        for i in range(n_elements):
            neighbors = find_neighbor_elements_enhanced(mesh_data, i)

            if len(neighbors) > 0:
                current_normal = mesh_data.element_normals[i]
                normal_variations = []

                for neighbor_idx in neighbors:
                    neighbor_normal = mesh_data.element_normals[neighbor_idx]
                    # 计算法向量夹角
                    cos_angle = np.dot(current_normal, neighbor_normal)
                    cos_angle = np.clip(cos_angle, -1.0, 1.0)
                    angle = np.arccos(cos_angle)
                    normal_variations.append(angle)

                normal_errors[i] = np.max(normal_variations)

        # 归一化
        if np.max(normal_errors) > 0:
            normal_errors = normal_errors / np.max(normal_errors)

        return normal_errors

    except Exception as e:
        print(f"   ⚠️ 法向量梯度误差计算失败: {e}")
        return np.zeros(n_elements)

def compute_size_variation_error(mesh_data: MeshData) -> np.ndarray:
    """计算尺寸变化误差"""
    n_elements = len(mesh_data.element_centers)
    size_errors = np.zeros(n_elements)

    try:
        # 计算每个单元的特征尺寸
        element_sizes = np.sqrt(mesh_data.element_areas)

        for i in range(n_elements):
            neighbors = find_neighbor_elements_enhanced(mesh_data, i)

            if len(neighbors) > 0:
                current_size = element_sizes[i]
                size_variations = []

                for neighbor_idx in neighbors:
                    neighbor_size = element_sizes[neighbor_idx]
                    size_ratio = abs(current_size - neighbor_size) / (current_size + 1e-12)
                    size_variations.append(size_ratio)

                size_errors[i] = np.max(size_variations)

        # 归一化
        if np.max(size_errors) > 0:
            size_errors = size_errors / np.max(size_errors)

        return size_errors

    except Exception as e:
        print(f"   ⚠️ 尺寸变化误差计算失败: {e}")
        return np.zeros(n_elements)

def find_neighbor_elements_enhanced(mesh_data: MeshData, element_idx: int) -> List[int]:
    """查找相邻单元（增强版）"""
    try:
        neighbors = []
        current_element = mesh_data.elements[element_idx]

        # 查找共享节点的单元
        for i, element in enumerate(mesh_data.elements):
            if i != element_idx:
                # 检查是否有共享节点
                shared_nodes = np.intersect1d(current_element, element)
                if len(shared_nodes) >= 2:  # 至少共享一条边
                    neighbors.append(i)

        # 如果没有找到足够的邻居，扩大搜索范围
        if len(neighbors) < 3:
            current_center = mesh_data.element_centers[element_idx]
            distances = []

            for i, center in enumerate(mesh_data.element_centers):
                if i != element_idx:
                    dist = np.linalg.norm(current_center - center)
                    distances.append((dist, i))

            # 按距离排序，选择最近的几个
            distances.sort()
            additional_neighbors = [idx for _, idx in distances[:5]]

            # 合并邻居列表
            neighbors = list(set(neighbors + additional_neighbors))

        return neighbors

    except Exception as e:
        print(f"   ⚠️ 增强相邻单元查找失败: {e}")
        return []

def compute_element_aspect_ratios(mesh_data: MeshData) -> np.ndarray:
    """计算单元长宽比"""
    n_elements = len(mesh_data.elements)
    aspect_ratios = np.ones(n_elements)

    try:
        for i, element in enumerate(mesh_data.elements):
            if len(element) >= 3:
                # 获取单元节点坐标
                element_nodes = mesh_data.nodes[element]

                # 计算边长
                edge_lengths = []
                for j in range(len(element)):
                    next_j = (j + 1) % len(element)
                    edge_length = np.linalg.norm(element_nodes[next_j] - element_nodes[j])
                    edge_lengths.append(edge_length)

                if len(edge_lengths) > 0:
                    max_edge = np.max(edge_lengths)
                    min_edge = np.min(edge_lengths)

                    if min_edge > 1e-12:
                        aspect_ratios[i] = max_edge / min_edge
                    else:
                        aspect_ratios[i] = 100.0  # 退化单元

        return aspect_ratios

    except Exception as e:
        print(f"   ⚠️ 长宽比计算失败: {e}")
        return np.ones(n_elements)

def refine_mesh_elements_enhanced(mesh_data: MeshData, refinement_flags: np.ndarray) -> MeshData:
    """细化指定的网格单元（增强版）"""
    try:
        if mesh_data.mesh_type == 'structured':
            return refine_structured_mesh_enhanced(mesh_data, refinement_flags)
        else:
            return refine_unstructured_mesh_enhanced(mesh_data, refinement_flags)

    except Exception as e:
        print(f"   ⚠️ 增强网格细化失败: {e}")
        return mesh_data

def refine_structured_mesh_enhanced(mesh_data: MeshData, refinement_flags: np.ndarray) -> MeshData:
    """细化结构化网格（增强版）"""
    try:
        # 分析需要细化的区域
        refinement_density = analyze_refinement_density(mesh_data, refinement_flags)

        # 计算新的网格密度
        base_density = estimate_base_mesh_density(mesh_data)
        new_density = apply_local_refinement(base_density, refinement_density)

        # 生成新的结构化网格
        refined_mesh = generate_refined_structured_mesh(mesh_data, new_density)

        return refined_mesh

    except Exception as e:
        print(f"   ⚠️ 增强结构化网格细化失败: {e}")
        return mesh_data

def refine_unstructured_mesh_enhanced(mesh_data: MeshData, refinement_flags: np.ndarray) -> MeshData:
    """细化非结构化网格（增强版）"""
    try:
        new_nodes = list(mesh_data.nodes)
        new_elements = []

        for i, element in enumerate(mesh_data.elements):
            if refinement_flags[i]:
                # 使用更智能的细化策略
                refined_sub_elements = refine_single_element_smart(element, mesh_data, new_nodes)
                new_elements.extend(refined_sub_elements)
            else:
                # 保持原单元
                new_elements.append(element)

        new_nodes = np.array(new_nodes)
        new_elements = np.array(new_elements)

        # 创建新的网格数据
        refined_mesh = MeshData(
            nodes=new_nodes,
            elements=new_elements,
            element_centers=np.zeros((len(new_elements), 3)),
            element_areas=np.zeros(len(new_elements)),
            element_normals=np.zeros((len(new_elements), 3)),
            mesh_type='unstructured'
        )

        # 重新计算几何属性
        calculate_element_properties_enhanced(refined_mesh)

        return refined_mesh

    except Exception as e:
        print(f"   ⚠️ 增强非结构化网格细化失败: {e}")
        return mesh_data

def analyze_refinement_density(mesh_data: MeshData, refinement_flags: np.ndarray) -> np.ndarray:
    """分析细化密度分布"""
    try:
        n_elements = len(mesh_data.element_centers)
        density_map = np.ones(n_elements)

        # 基于细化标志计算密度
        for i in range(n_elements):
            if refinement_flags[i]:
                density_map[i] = 2.0  # 需要细化的区域密度加倍

                # 影响相邻区域
                neighbors = find_neighbor_elements_enhanced(mesh_data, i)
                for neighbor_idx in neighbors:
                    density_map[neighbor_idx] = max(density_map[neighbor_idx], 1.5)

        return density_map

    except Exception as e:
        print(f"   ⚠️ 细化密度分析失败: {e}")
        return np.ones(len(mesh_data.element_centers))

def estimate_base_mesh_density(mesh_data: MeshData) -> Dict[str, float]:
    """估计基础网格密度"""
    try:
        # 计算平均单元尺寸
        avg_area = np.mean(mesh_data.element_areas)
        avg_size = np.sqrt(avg_area)

        # 估计网格分辨率
        bbox_min = np.min(mesh_data.nodes, axis=0)
        bbox_max = np.max(mesh_data.nodes, axis=0)
        bbox_size = bbox_max - bbox_min

        density_info = {
            'avg_element_size': avg_size,
            'bbox_size': bbox_size,
            'total_elements': len(mesh_data.elements),
            'density_factor': 1.0
        }

        return density_info

    except Exception as e:
        print(f"   ⚠️ 基础密度估计失败: {e}")
        return {'density_factor': 1.0}

def apply_local_refinement(base_density: Dict, refinement_density: np.ndarray) -> Dict:
    """应用局部细化"""
    try:
        # 计算新的密度参数
        max_refinement = np.max(refinement_density)
        avg_refinement = np.mean(refinement_density)

        new_density = base_density.copy()
        new_density['density_factor'] = base_density.get('density_factor', 1.0) * avg_refinement
        new_density['max_local_factor'] = max_refinement

        return new_density

    except Exception as e:
        print(f"   ⚠️ 局部细化应用失败: {e}")
        return base_density
