"""
配置管理器
=========

提供统一的配置管理功能

基于adevice_complement4.md技术规范实现
"""

import json
import yaml
import os
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import warnings


class ConfigManager:
    """
    配置管理器（基于adevice_complement4.md规范）
    
    提供配置文件加载、验证、合并和管理功能
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir) if config_dir else Path.cwd() / "configs"
        self.configs = {}
        self.default_configs = {}
        self.validation_schemas = {}
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载默认配置
        self._load_default_configs()
        
        print(f"✅ 配置管理器初始化完成")
        print(f"   配置目录: {self.config_dir}")
    
    def _load_default_configs(self):
        """加载默认配置"""
        try:
            from .default_configs import DefaultConfigs
            default_config_manager = DefaultConfigs()
            self.default_configs = default_config_manager.get_all_defaults()
            
            print(f"   ✅ 加载默认配置: {len(self.default_configs)} 个类别")
            
        except Exception as e:
            warnings.warn(f"默认配置加载失败: {e}")
    
    def load_config(self, config_name: str, config_file: Optional[str] = None) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置名称
            config_file: 配置文件路径（可选）
            
        Returns:
            config: 配置字典
        """
        try:
            if config_file is None:
                # 尝试多种文件格式
                for ext in ['.yaml', '.yml', '.json']:
                    config_file = self.config_dir / f"{config_name}{ext}"
                    if config_file.exists():
                        break
                else:
                    # 如果没有找到配置文件，使用默认配置
                    if config_name in self.default_configs:
                        config = self.default_configs[config_name].copy()
                        self.configs[config_name] = config
                        print(f"   ✅ 使用默认配置: {config_name}")
                        return config
                    else:
                        raise FileNotFoundError(f"配置文件未找到: {config_name}")
            
            # 加载配置文件
            config_path = Path(config_file)
            
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            # 与默认配置合并
            if config_name in self.default_configs:
                merged_config = self._merge_configs(self.default_configs[config_name], config)
                config = merged_config
            
            # 验证配置
            self._validate_config(config_name, config)
            
            # 存储配置
            self.configs[config_name] = config
            
            print(f"   ✅ 加载配置文件: {config_path}")
            return config
            
        except Exception as e:
            print(f"   ❌ 配置加载失败: {e}")
            raise
    
    def save_config(self, config_name: str, config: Dict[str, Any], 
                   config_file: Optional[str] = None, format: str = 'yaml'):
        """
        保存配置文件
        
        Args:
            config_name: 配置名称
            config: 配置字典
            config_file: 配置文件路径（可选）
            format: 文件格式 ('yaml', 'json')
        """
        try:
            if config_file is None:
                ext = '.yaml' if format == 'yaml' else '.json'
                config_file = self.config_dir / f"{config_name}{ext}"
            
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format == 'yaml':
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            elif format == 'json':
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的文件格式: {format}")
            
            # 更新内存中的配置
            self.configs[config_name] = config
            
            print(f"   ✅ 保存配置文件: {config_path}")
            
        except Exception as e:
            print(f"   ❌ 配置保存失败: {e}")
            raise
    
    def get_config(self, config_name: str) -> Dict[str, Any]:
        """
        获取配置
        
        Args:
            config_name: 配置名称
            
        Returns:
            config: 配置字典
        """
        if config_name not in self.configs:
            return self.load_config(config_name)
        
        return self.configs[config_name]
    
    def update_config(self, config_name: str, updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_name: 配置名称
            updates: 更新字典
        """
        try:
            if config_name not in self.configs:
                self.load_config(config_name)
            
            # 深度合并更新
            self.configs[config_name] = self._merge_configs(self.configs[config_name], updates)
            
            # 验证更新后的配置
            self._validate_config(config_name, self.configs[config_name])
            
            print(f"   ✅ 更新配置: {config_name}")
            
        except Exception as e:
            print(f"   ❌ 配置更新失败: {e}")
            raise
    
    def _merge_configs(self, base_config: Dict[str, Any], 
                      override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并配置
        
        Args:
            base_config: 基础配置
            override_config: 覆盖配置
            
        Returns:
            merged_config: 合并后的配置
        """
        merged = base_config.copy()
        
        for key, value in override_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                # 递归合并字典
                merged[key] = self._merge_configs(merged[key], value)
            else:
                # 直接覆盖
                merged[key] = value
        
        return merged
    
    def _validate_config(self, config_name: str, config: Dict[str, Any]):
        """
        验证配置
        
        Args:
            config_name: 配置名称
            config: 配置字典
        """
        try:
            if config_name in self.validation_schemas:
                schema = self.validation_schemas[config_name]
                self._validate_against_schema(config, schema)
            else:
                # 基本验证
                self._basic_validation(config)
                
        except Exception as e:
            warnings.warn(f"配置验证失败: {e}")
    
    def _validate_against_schema(self, config: Dict[str, Any], schema: Dict[str, Any]):
        """根据模式验证配置"""
        # 简化的模式验证实现
        for key, expected_type in schema.items():
            if key in config:
                if not isinstance(config[key], expected_type):
                    raise ValueError(f"配置项 {key} 类型错误，期望 {expected_type}，实际 {type(config[key])}")
    
    def _basic_validation(self, config: Dict[str, Any]):
        """基本配置验证"""
        # 检查必需的配置项
        required_keys = ['simulation', 'geometry', 'physics']
        
        for key in required_keys:
            if key not in config:
                warnings.warn(f"缺少必需的配置项: {key}")
    
    def register_validation_schema(self, config_name: str, schema: Dict[str, Any]):
        """
        注册验证模式
        
        Args:
            config_name: 配置名称
            schema: 验证模式
        """
        self.validation_schemas[config_name] = schema
        print(f"   ✅ 注册验证模式: {config_name}")
    
    def list_configs(self) -> List[str]:
        """
        列出所有可用的配置
        
        Returns:
            config_names: 配置名称列表
        """
        # 从文件系统扫描配置文件
        file_configs = []
        for ext in ['.yaml', '.yml', '.json']:
            file_configs.extend([f.stem for f in self.config_dir.glob(f"*{ext}")])
        
        # 合并内存中的配置和默认配置
        all_configs = set(file_configs) | set(self.configs.keys()) | set(self.default_configs.keys())
        
        return sorted(list(all_configs))
    
    def export_config(self, config_name: str, export_path: str, format: str = 'yaml'):
        """
        导出配置到指定路径
        
        Args:
            config_name: 配置名称
            export_path: 导出路径
            format: 导出格式
        """
        try:
            config = self.get_config(config_name)
            self.save_config(config_name, config, export_path, format)
            print(f"   ✅ 导出配置: {config_name} -> {export_path}")
            
        except Exception as e:
            print(f"   ❌ 配置导出失败: {e}")
            raise
    
    def create_config_template(self, config_name: str, template_path: str):
        """
        创建配置模板
        
        Args:
            config_name: 配置名称
            template_path: 模板路径
        """
        try:
            if config_name in self.default_configs:
                template_config = self.default_configs[config_name].copy()
                
                # 添加注释和说明
                template_config['_template_info'] = {
                    'description': f'Configuration template for {config_name}',
                    'created_by': 'ConfigManager',
                    'version': '1.0'
                }
                
                self.save_config(config_name, template_config, template_path, 'yaml')
                print(f"   ✅ 创建配置模板: {template_path}")
            else:
                raise ValueError(f"没有找到默认配置: {config_name}")
                
        except Exception as e:
            print(f"   ❌ 配置模板创建失败: {e}")
            raise


# 全局配置管理器实例
_global_config_manager = None

def get_config_manager(config_dir: Optional[str] = None) -> ConfigManager:
    """获取全局配置管理器实例"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager(config_dir)
    return _global_config_manager

def load_config(config_name: str, config_file: Optional[str] = None) -> Dict[str, Any]:
    """便捷的配置加载函数"""
    manager = get_config_manager()
    return manager.load_config(config_name, config_file)
