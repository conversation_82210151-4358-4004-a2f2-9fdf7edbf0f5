"""
涡核模型 - 完全复刻原始算法
==========================

实现各种涡核模型，包括Vatistas、Rankine、Lamb-Oseen等模型。
基于原始 cycloidal_rotor_suite 项目的完整实现。

主要功能：
- Vatistas涡核模型（可调参数n）
- Rankine涡核模型（刚体旋转+势流）
- Lamb-Oseen涡核模型（粘性扩散）
- 奇点正则化处理
- 涡核尺寸自适应计算

基于原始项目的完整实现，确保与原始算法在数值层面完全一致。

作者: Augment Agent
日期: 2025-08-03
"""

import warnings
from typing import Dict, Optional, Tuple, Union
from abc import ABC, abstractmethod

import numpy as np


class VortexCoreModel(ABC):
    """涡核模型基类 - 完全复刻原始实现"""
    
    def __init__(self, core_radius: float = 0.01):
        """
        初始化涡核模型
        
        Args:
            core_radius: 涡核半径 [m]
        """
        self.core_radius = core_radius
        self.numerical_tolerance = 1e-12
    
    @abstractmethod
    def calculate_velocity(self, r: Union[float, np.ndarray], 
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算涡核诱导速度
        
        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]
            
        Returns:
            velocity: 诱导速度 [m/s]
        """
        pass
    
    @abstractmethod
    def get_model_name(self) -> str:
        """获取模型名称"""
        pass


class VatistasVortexCore(VortexCoreModel):
    """
    Vatistas涡核模型 - 完全复刻原始算法
    
    速度分布：v_θ = (Γ/2π) * r / (r_c^n + r^n)^(1/n)
    其中n是可调参数，通常取1.5-2.0
    """
    
    def __init__(self, core_radius: float = 0.01, n_parameter: float = 2.0):
        """
        初始化Vatistas涡核模型
        
        Args:
            core_radius: 涡核半径 r_c [m]
            n_parameter: Vatistas参数 n
        """
        super().__init__(core_radius)
        self.n_parameter = n_parameter
        
        # 参数验证
        if self.n_parameter <= 0:
            raise ValueError("Vatistas参数n必须为正数")
        if self.n_parameter < 1.0:
            warnings.warn("Vatistas参数n < 1可能导致数值不稳定")
    
    def calculate_velocity(self, r: Union[float, np.ndarray], 
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算Vatistas涡核诱导速度 - 完全复刻原始算法
        
        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]
            
        Returns:
            velocity: 诱导速度 [m/s]
        """
        # 转换为numpy数组以支持向量化计算
        r_array = np.asarray(r)
        
        # 避免除零
        r_safe = np.maximum(r_array, self.numerical_tolerance)
        
        # Vatistas公式
        r_c_n = self.core_radius ** self.n_parameter
        r_n = r_safe ** self.n_parameter
        denominator = (r_c_n + r_n) ** (1.0 / self.n_parameter)
        
        # 避免除零
        denominator_safe = np.maximum(denominator, self.numerical_tolerance)
        
        velocity = (circulation / (2 * np.pi)) * r_safe / denominator_safe
        
        # 如果输入是标量，返回标量
        if np.isscalar(r):
            return float(velocity)
        else:
            return velocity
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return f"Vatistas(n={self.n_parameter:.1f})"


class RankineVortexCore(VortexCoreModel):
    """
    Rankine涡核模型 - 完全复刻原始算法
    
    速度分布：
    - r < r_c: v_θ = (Γ/2π) * r / r_c²  (刚体旋转)
    - r ≥ r_c: v_θ = Γ / (2π * r)       (势流)
    """
    
    def __init__(self, core_radius: float = 0.01):
        """
        初始化Rankine涡核模型
        
        Args:
            core_radius: 涡核半径 r_c [m]
        """
        super().__init__(core_radius)
    
    def calculate_velocity(self, r: Union[float, np.ndarray], 
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算Rankine涡核诱导速度 - 完全复刻原始算法
        
        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]
            
        Returns:
            velocity: 诱导速度 [m/s]
        """
        # 转换为numpy数组以支持向量化计算
        r_array = np.asarray(r)
        
        # 避免除零
        r_safe = np.maximum(r_array, self.numerical_tolerance)
        
        # 分段函数
        velocity = np.zeros_like(r_safe)
        
        # 内部区域（刚体旋转）
        inner_mask = r_safe < self.core_radius
        velocity[inner_mask] = (circulation / (2 * np.pi)) * r_safe[inner_mask] / (self.core_radius ** 2)
        
        # 外部区域（势流）
        outer_mask = r_safe >= self.core_radius
        velocity[outer_mask] = circulation / (2 * np.pi * r_safe[outer_mask])
        
        # 如果输入是标量，返回标量
        if np.isscalar(r):
            return float(velocity)
        else:
            return velocity
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return "Rankine"


class LambOseenVortexCore(VortexCoreModel):
    """
    Lamb-Oseen涡核模型 - 完全复刻原始算法
    
    速度分布：v_θ = (Γ/2π) * (1 - exp(-r²/r_c²)) / r
    考虑粘性扩散效应的涡核模型
    """
    
    def __init__(self, core_radius: float = 0.01, viscosity: float = 1.81e-5):
        """
        初始化Lamb-Oseen涡核模型
        
        Args:
            core_radius: 涡核半径 r_c [m]
            viscosity: 动力粘度 [Pa·s]
        """
        super().__init__(core_radius)
        self.viscosity = viscosity
    
    def calculate_velocity(self, r: Union[float, np.ndarray], 
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算Lamb-Oseen涡核诱导速度 - 完全复刻原始算法
        
        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]
            
        Returns:
            velocity: 诱导速度 [m/s]
        """
        # 转换为numpy数组以支持向量化计算
        r_array = np.asarray(r)
        
        # 避免除零
        r_safe = np.maximum(r_array, self.numerical_tolerance)
        
        # Lamb-Oseen公式
        r_ratio_squared = (r_safe / self.core_radius) ** 2
        exponential_term = 1.0 - np.exp(-r_ratio_squared)
        
        velocity = (circulation / (2 * np.pi)) * exponential_term / r_safe
        
        # 如果输入是标量，返回标量
        if np.isscalar(r):
            return float(velocity)
        else:
            return velocity
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return "Lamb-Oseen"


class AdaptiveVortexCore(VortexCoreModel):
    """
    自适应涡核模型 - 完全复刻原始算法
    
    根据局部网格尺寸和物理参数自动调整涡核半径
    """
    
    def __init__(self, base_core_radius: float = 0.01, 
                 adaptation_factor: float = 2.0,
                 base_model: str = 'vatistas'):
        """
        初始化自适应涡核模型
        
        Args:
            base_core_radius: 基础涡核半径 [m]
            adaptation_factor: 自适应因子
            base_model: 基础涡核模型类型
        """
        super().__init__(base_core_radius)
        self.adaptation_factor = adaptation_factor
        self.base_model_type = base_model
        
        # 创建基础涡核模型
        if base_model == 'vatistas':
            self.base_model = VatistasVortexCore(base_core_radius)
        elif base_model == 'rankine':
            self.base_model = RankineVortexCore(base_core_radius)
        elif base_model == 'lamb_oseen':
            self.base_model = LambOseenVortexCore(base_core_radius)
        else:
            raise ValueError(f"不支持的基础涡核模型: {base_model}")
    
    def calculate_velocity(self, r: Union[float, np.ndarray], 
                          circulation: float,
                          local_mesh_size: Optional[float] = None) -> Union[float, np.ndarray]:
        """
        计算自适应涡核诱导速度 - 完全复刻原始算法
        
        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]
            local_mesh_size: 局部网格尺寸 [m]
            
        Returns:
            velocity: 诱导速度 [m/s]
        """
        # 自适应涡核半径
        if local_mesh_size is not None:
            adaptive_radius = max(self.core_radius, 
                                self.adaptation_factor * local_mesh_size)
        else:
            adaptive_radius = self.core_radius
        
        # 更新基础模型的涡核半径
        original_radius = self.base_model.core_radius
        self.base_model.core_radius = adaptive_radius
        
        # 计算速度
        velocity = self.base_model.calculate_velocity(r, circulation)
        
        # 恢复原始涡核半径
        self.base_model.core_radius = original_radius
        
        return velocity
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return f"Adaptive-{self.base_model.get_model_name()}"


class VortexCoreManager:
    """
    涡核模型管理器 - 完全复刻原始算法
    
    统一管理不同类型的涡核模型
    """
    
    def __init__(self, config: Dict):
        """
        初始化涡核模型管理器
        
        Args:
            config: 涡核模型配置
        """
        self.config = config
        
        # 默认参数
        self.default_core_radius = config.get('default_core_radius', 0.01)
        self.model_type = config.get('vortex_core_model', 'vatistas')
        
        # 模型特定参数
        self.vatistas_n = config.get('vatistas_n_parameter', 2.0)
        self.viscosity = config.get('viscosity', 1.81e-5)
        self.adaptation_factor = config.get('adaptation_factor', 2.0)
        
        # 创建涡核模型
        self.vortex_model = self._create_vortex_model()
        
        print(f"✅ 涡核模型管理器初始化完成")
        print(f"   模型类型: {self.vortex_model.get_model_name()}")
        print(f"   涡核半径: {self.default_core_radius:.4f} m")
    
    def _create_vortex_model(self) -> VortexCoreModel:
        """创建涡核模型"""
        if self.model_type == 'vatistas':
            return VatistasVortexCore(self.default_core_radius, self.vatistas_n)
        elif self.model_type == 'rankine':
            return RankineVortexCore(self.default_core_radius)
        elif self.model_type == 'lamb_oseen':
            return LambOseenVortexCore(self.default_core_radius, self.viscosity)
        elif self.model_type == 'adaptive':
            base_model = self.config.get('adaptive_base_model', 'vatistas')
            return AdaptiveVortexCore(self.default_core_radius, 
                                    self.adaptation_factor, base_model)
        else:
            raise ValueError(f"不支持的涡核模型类型: {self.model_type}")
    
    def calculate_induced_velocity(self, r: Union[float, np.ndarray], 
                                 circulation: float,
                                 **kwargs) -> Union[float, np.ndarray]:
        """
        计算诱导速度
        
        Args:
            r: 距离 [m]
            circulation: 环量 [m²/s]
            **kwargs: 额外参数（如local_mesh_size）
            
        Returns:
            velocity: 诱导速度 [m/s]
        """
        if isinstance(self.vortex_model, AdaptiveVortexCore):
            return self.vortex_model.calculate_velocity(r, circulation, **kwargs)
        else:
            return self.vortex_model.calculate_velocity(r, circulation)
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'model_type': self.model_type,
            'model_name': self.vortex_model.get_model_name(),
            'core_radius': self.default_core_radius,
            'vatistas_n': getattr(self.vortex_model, 'n_parameter', None),
            'viscosity': getattr(self.vortex_model, 'viscosity', None),
            'adaptation_factor': getattr(self.vortex_model, 'adaptation_factor', None)
        }


# 工厂函数
def create_vatistas_model(core_radius: float = 0.01, n_parameter: float = 2.0) -> VatistasVortexCore:
    """创建Vatistas涡核模型"""
    return VatistasVortexCore(core_radius, n_parameter)


def create_rankine_model(core_radius: float = 0.01) -> RankineVortexCore:
    """创建Rankine涡核模型"""
    return RankineVortexCore(core_radius)


def create_lamb_oseen_model(core_radius: float = 0.01, viscosity: float = 1.81e-5) -> LambOseenVortexCore:
    """创建Lamb-Oseen涡核模型"""
    return LambOseenVortexCore(core_radius, viscosity)


def create_adaptive_model(base_core_radius: float = 0.01, 
                         adaptation_factor: float = 2.0,
                         base_model: str = 'vatistas') -> AdaptiveVortexCore:
    """创建自适应涡核模型"""
    return AdaptiveVortexCore(base_core_radius, adaptation_factor, base_model)


def create_vortex_core_manager(config: Dict) -> VortexCoreManager:
    """创建涡核模型管理器"""
    return VortexCoreManager(config)


# ==================== 增强的涡核模型（基于adevice_complement4.md规范） ====================

class ScallyVortexCore(VortexCoreModel):
    """
    Scally涡核模型（基于adevice_complement4.md规范）

    速度分布：v_θ = (Γ/2π) * r / (r_c² + r²)
    适用于高雷诺数流动的涡核建模
    """

    def __init__(self, core_radius: float = 0.01):
        """
        初始化Scally涡核模型

        Args:
            core_radius: 涡核半径 [m]
        """
        super().__init__(core_radius)

    def calculate_velocity(self, r: Union[float, np.ndarray],
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算Scally涡核诱导速度

        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]

        Returns:
            velocity: 切向速度 [m/s]
        """
        try:
            # 确保输入为numpy数组
            r_array = np.atleast_1d(r)

            # Scally涡核公式
            denominator = self.core_radius**2 + r_array**2
            velocity = (circulation / (2 * np.pi)) * r_array / denominator

            # 处理奇点
            velocity = np.where(denominator < self.numerical_tolerance, 0.0, velocity)

            # 返回标量或数组
            return velocity.item() if np.isscalar(r) else velocity

        except Exception as e:
            warnings.warn(f"Scally涡核速度计算失败: {e}")
            return np.zeros_like(r) if hasattr(r, '__len__') else 0.0

    def get_model_name(self) -> str:
        """获取模型名称"""
        return "Scally"

class KaufmannVortexCore(VortexCoreModel):
    """
    Kaufmann涡核模型（基于adevice_complement4.md规范）

    速度分布：v_θ = (Γ/2π) * r * (1 - exp(-r²/r_c²)) / r_c²
    考虑涡核演化的高级模型
    """

    def __init__(self, core_radius: float = 0.01, evolution_parameter: float = 1.0):
        """
        初始化Kaufmann涡核模型

        Args:
            core_radius: 涡核半径 [m]
            evolution_parameter: 演化参数
        """
        super().__init__(core_radius)
        self.evolution_parameter = evolution_parameter

    def calculate_velocity(self, r: Union[float, np.ndarray],
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算Kaufmann涡核诱导速度

        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]

        Returns:
            velocity: 切向速度 [m/s]
        """
        try:
            # 确保输入为numpy数组
            r_array = np.atleast_1d(r)

            # 避免除零
            safe_r = np.where(r_array < self.numerical_tolerance,
                            self.numerical_tolerance, r_array)

            # Kaufmann涡核公式
            r_normalized = safe_r / self.core_radius
            exp_term = np.exp(-self.evolution_parameter * r_normalized**2)
            velocity = (circulation / (2 * np.pi)) * (1 - exp_term) / safe_r

            # 处理中心奇点
            velocity = np.where(r_array < self.numerical_tolerance, 0.0, velocity)

            # 返回标量或数组
            return velocity.item() if np.isscalar(r) else velocity

        except Exception as e:
            warnings.warn(f"Kaufmann涡核速度计算失败: {e}")
            return np.zeros_like(r) if hasattr(r, '__len__') else 0.0

    def get_model_name(self) -> str:
        """获取模型名称"""
        return f"Kaufmann(α={self.evolution_parameter:.1f})"

class CompressibleVortexCore(VortexCoreModel):
    """
    可压缩涡核模型（基于adevice_complement4.md规范）

    考虑可压缩性效应的涡核模型，适用于高马赫数流动
    """

    def __init__(self, core_radius: float = 0.01, mach_number: float = 0.3,
                 gamma: float = 1.4):
        """
        初始化可压缩涡核模型

        Args:
            core_radius: 涡核半径 [m]
            mach_number: 马赫数
            gamma: 比热比
        """
        super().__init__(core_radius)
        self.mach_number = mach_number
        self.gamma = gamma

        # 计算可压缩性修正因子
        self.compressibility_factor = self._compute_compressibility_factor()

    def _compute_compressibility_factor(self) -> float:
        """计算可压缩性修正因子"""
        if self.mach_number < 0.3:
            # 低马赫数：可压缩性效应可忽略
            return 1.0
        else:
            # 高马赫数：应用Prandtl-Glauert修正
            beta_squared = 1.0 - self.mach_number**2
            if beta_squared > 0:
                return 1.0 / np.sqrt(beta_squared)
            else:
                # 超声速情况
                return 1.0 / np.sqrt(abs(beta_squared))

    def calculate_velocity(self, r: Union[float, np.ndarray],
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算可压缩涡核诱导速度

        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]

        Returns:
            velocity: 切向速度 [m/s]
        """
        try:
            # 确保输入为numpy数组
            r_array = np.atleast_1d(r)

            # 基础Rankine涡核
            velocity = np.zeros_like(r_array)

            # 内部区域（刚体旋转）
            inner_mask = r_array <= self.core_radius
            if np.any(inner_mask):
                velocity[inner_mask] = (circulation / (2 * np.pi)) * \
                                     r_array[inner_mask] / self.core_radius**2

            # 外部区域（势流）
            outer_mask = r_array > self.core_radius
            if np.any(outer_mask):
                velocity[outer_mask] = (circulation / (2 * np.pi)) / r_array[outer_mask]

            # 应用可压缩性修正
            velocity *= self.compressibility_factor

            # 返回标量或数组
            return velocity.item() if np.isscalar(r) else velocity

        except Exception as e:
            warnings.warn(f"可压缩涡核速度计算失败: {e}")
            return np.zeros_like(r) if hasattr(r, '__len__') else 0.0

    def update_mach_number(self, new_mach_number: float):
        """更新马赫数"""
        self.mach_number = new_mach_number
        self.compressibility_factor = self._compute_compressibility_factor()

    def get_model_name(self) -> str:
        """获取模型名称"""
        return f"Compressible(M={self.mach_number:.2f})"

class TimeEvolvingVortexCore(VortexCoreModel):
    """
    时间演化涡核模型（基于adevice_complement4.md规范）

    考虑涡核随时间演化的动态模型
    """

    def __init__(self, initial_core_radius: float = 0.01,
                 viscosity: float = 1.5e-5, age: float = 0.0):
        """
        初始化时间演化涡核模型

        Args:
            initial_core_radius: 初始涡核半径 [m]
            viscosity: 运动粘度 [m²/s]
            age: 涡核年龄 [s]
        """
        super().__init__(initial_core_radius)
        self.initial_core_radius = initial_core_radius
        self.viscosity = viscosity
        self.age = age

        # 更新当前涡核半径
        self._update_core_radius()

    def _update_core_radius(self):
        """更新涡核半径（基于粘性扩散）"""
        # Lamb-Oseen涡核的粘性扩散
        self.core_radius = np.sqrt(self.initial_core_radius**2 + 4 * self.viscosity * self.age)

    def calculate_velocity(self, r: Union[float, np.ndarray],
                          circulation: float) -> Union[float, np.ndarray]:
        """
        计算时间演化涡核诱导速度

        Args:
            r: 距离涡核中心的距离 [m]
            circulation: 环量 [m²/s]

        Returns:
            velocity: 切向速度 [m/s]
        """
        try:
            # 确保输入为numpy数组
            r_array = np.atleast_1d(r)

            # 避免除零
            safe_r = np.where(r_array < self.numerical_tolerance,
                            self.numerical_tolerance, r_array)

            # 时间演化的Lamb-Oseen涡核
            r_normalized_sq = (safe_r / self.core_radius)**2
            exp_term = np.exp(-r_normalized_sq)
            velocity = (circulation / (2 * np.pi)) * (1 - exp_term) / safe_r

            # 处理中心奇点
            velocity = np.where(r_array < self.numerical_tolerance, 0.0, velocity)

            # 返回标量或数组
            return velocity.item() if np.isscalar(r) else velocity

        except Exception as e:
            warnings.warn(f"时间演化涡核速度计算失败: {e}")
            return np.zeros_like(r) if hasattr(r, '__len__') else 0.0

    def advance_time(self, dt: float):
        """推进时间"""
        self.age += dt
        self._update_core_radius()

    def get_model_name(self) -> str:
        """获取模型名称"""
        return f"TimeEvolving(age={self.age:.3f}s)"


# 增强的涡核模型工厂函数

def create_enhanced_vortex_core_model(model_type: str, **kwargs) -> VortexCoreModel:
    """
    创建增强的涡核模型

    Args:
        model_type: 模型类型 ('scally', 'kaufmann', 'compressible', 'time_evolving')
        **kwargs: 模型参数

    Returns:
        VortexCoreModel: 涡核模型实例
    """
    if model_type == 'scally':
        core_radius = kwargs.get('core_radius', 0.01)
        return ScallyVortexCore(core_radius)

    elif model_type == 'kaufmann':
        core_radius = kwargs.get('core_radius', 0.01)
        evolution_parameter = kwargs.get('evolution_parameter', 1.0)
        return KaufmannVortexCore(core_radius, evolution_parameter)

    elif model_type == 'compressible':
        core_radius = kwargs.get('core_radius', 0.01)
        mach_number = kwargs.get('mach_number', 0.3)
        gamma = kwargs.get('gamma', 1.4)
        return CompressibleVortexCore(core_radius, mach_number, gamma)

    elif model_type == 'time_evolving':
        initial_core_radius = kwargs.get('initial_core_radius', 0.01)
        viscosity = kwargs.get('viscosity', 1.5e-5)
        age = kwargs.get('age', 0.0)
        return TimeEvolvingVortexCore(initial_core_radius, viscosity, age)

    else:
        raise ValueError(f"不支持的增强涡核模型类型: {model_type}")

def create_optimal_vortex_core_model(flow_conditions: Dict, **kwargs) -> VortexCoreModel:
    """
    根据流动条件创建最优涡核模型

    Args:
        flow_conditions: 流动条件字典
        **kwargs: 额外参数

    Returns:
        VortexCoreModel: 最优涡核模型实例
    """
    mach_number = flow_conditions.get('mach_number', 0.3)
    reynolds_number = flow_conditions.get('reynolds_number', 1e5)
    time_scale = flow_conditions.get('time_scale', 0.0)
    core_radius = kwargs.get('core_radius', 0.01)

    # 决策逻辑
    if mach_number > 0.5:
        # 高马赫数：使用可压缩涡核模型
        print(f"   选择可压缩涡核模型 (M={mach_number:.2f})")
        return CompressibleVortexCore(core_radius, mach_number)

    elif time_scale > 0.1:
        # 长时间尺度：使用时间演化模型
        viscosity = kwargs.get('viscosity', 1.5e-5)
        print(f"   选择时间演化涡核模型 (t={time_scale:.3f}s)")
        return TimeEvolvingVortexCore(core_radius, viscosity, time_scale)

    elif reynolds_number > 1e6:
        # 高雷诺数：使用Kaufmann模型
        evolution_parameter = kwargs.get('evolution_parameter', 1.5)
        print(f"   选择Kaufmann涡核模型 (Re={reynolds_number:.0e})")
        return KaufmannVortexCore(core_radius, evolution_parameter)

    elif reynolds_number > 1e4:
        # 中等雷诺数：使用Scally模型
        print(f"   选择Scally涡核模型 (Re={reynolds_number:.0e})")
        return ScallyVortexCore(core_radius)

    else:
        # 默认：使用Vatistas模型
        n_parameter = kwargs.get('n_parameter', 2.0)
        print(f"   选择Vatistas涡核模型 (默认选择)")
        return VatistasVortexCore(core_radius, n_parameter)
