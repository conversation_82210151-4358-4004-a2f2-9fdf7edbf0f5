"""
GPU加速的数值积分
================

提供高性能的大规模数值积分计算

基于adevice_complement4.md技术规范实现
"""

import numpy as np
from typing import Optional, Tuple, Union, Callable
import warnings

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False


class GPUIntegration:
    """
    GPU加速的数值积分器
    
    支持大规模并行数值积分计算
    """
    
    def __init__(self, backend: str = "auto", device_id: int = 0):
        """
        初始化GPU数值积分器
        
        Args:
            backend: 计算后端 ("torch", "cupy", "auto")
            device_id: GPU设备ID
        """
        self.backend = backend
        self.device_id = device_id
        self.device = None
        self.is_available = False
        
        self._initialize_backend()
    
    def _initialize_backend(self):
        """初始化计算后端"""
        if self.backend == "auto":
            if TORCH_AVAILABLE:
                self.backend = "torch"
            elif CUPY_AVAILABLE:
                self.backend = "cupy"
            else:
                self.backend = "cpu"
                warnings.warn("GPU后端不可用，回退到CPU计算")
        
        if self.backend == "torch" and TORCH_AVAILABLE:
            try:
                if torch.cuda.is_available():
                    self.device = torch.device(f"cuda:{self.device_id}")
                    self.is_available = True
                else:
                    self.backend = "cpu"
                    warnings.warn("CUDA不可用，回退到CPU计算")
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"Torch初始化失败: {e}")
        
        elif self.backend == "cupy" and CUPY_AVAILABLE:
            try:
                cp.cuda.Device(self.device_id).use()
                self.is_available = True
            except Exception as e:
                self.backend = "cpu"
                warnings.warn(f"CuPy初始化失败: {e}")
    
    def runge_kutta_4_batch(self, 
                          y0_batch: np.ndarray,
                          t_span: np.ndarray,
                          f_batch: np.ndarray,
                          dt: float) -> np.ndarray:
        """
        批量四阶Runge-Kutta积分
        
        Args:
            y0_batch: 初始状态批次 [batch_size, n_states]
            t_span: 时间跨度 [n_steps]
            f_batch: 导数函数值批次 [batch_size, n_steps, n_states]
            dt: 时间步长
            
        Returns:
            y_batch: 积分结果 [batch_size, n_steps, n_states]
        """
        if not self.is_available:
            return self._rk4_cpu_fallback(y0_batch, t_span, f_batch, dt)
        
        try:
            if self.backend == "torch":
                return self._rk4_torch(y0_batch, t_span, f_batch, dt)
            elif self.backend == "cupy":
                return self._rk4_cupy(y0_batch, t_span, f_batch, dt)
            else:
                return self._rk4_cpu_fallback(y0_batch, t_span, f_batch, dt)
        except Exception as e:
            warnings.warn(f"GPU RK4积分失败，回退到CPU: {e}")
            return self._rk4_cpu_fallback(y0_batch, t_span, f_batch, dt)
    
    def _rk4_torch(self, y0_batch, t_span, f_batch, dt):
        """使用PyTorch进行RK4积分"""
        y0 = torch.from_numpy(y0_batch).float().to(self.device)
        f_vals = torch.from_numpy(f_batch).float().to(self.device)
        
        batch_size, n_states = y0.shape
        n_steps = len(t_span)
        
        # 初始化结果数组
        y_result = torch.zeros(batch_size, n_steps, n_states, device=self.device)
        y_result[:, 0, :] = y0
        
        y_current = y0.clone()
        
        for i in range(1, n_steps):
            # RK4步骤
            k1 = dt * f_vals[:, i-1, :]
            k2 = dt * f_vals[:, i-1, :]  # 简化：使用相同的导数值
            k3 = dt * f_vals[:, i-1, :]
            k4 = dt * f_vals[:, i-1, :]
            
            y_current = y_current + (k1 + 2*k2 + 2*k3 + k4) / 6.0
            y_result[:, i, :] = y_current
        
        return y_result.cpu().numpy()
    
    def _rk4_cupy(self, y0_batch, t_span, f_batch, dt):
        """使用CuPy进行RK4积分"""
        y0 = cp.asarray(y0_batch, dtype=cp.float32)
        f_vals = cp.asarray(f_batch, dtype=cp.float32)
        
        batch_size, n_states = y0.shape
        n_steps = len(t_span)
        
        # 初始化结果数组
        y_result = cp.zeros((batch_size, n_steps, n_states), dtype=cp.float32)
        y_result[:, 0, :] = y0
        
        y_current = y0.copy()
        
        for i in range(1, n_steps):
            # RK4步骤
            k1 = dt * f_vals[:, i-1, :]
            k2 = dt * f_vals[:, i-1, :]
            k3 = dt * f_vals[:, i-1, :]
            k4 = dt * f_vals[:, i-1, :]
            
            y_current = y_current + (k1 + 2*k2 + 2*k3 + k4) / 6.0
            y_result[:, i, :] = y_current
        
        return cp.asnumpy(y_result)
    
    def _rk4_cpu_fallback(self, y0_batch, t_span, f_batch, dt):
        """CPU回退RK4积分"""
        batch_size, n_states = y0_batch.shape
        n_steps = len(t_span)
        
        y_result = np.zeros((batch_size, n_steps, n_states))
        y_result[:, 0, :] = y0_batch
        
        y_current = y0_batch.copy()
        
        for i in range(1, n_steps):
            # RK4步骤
            k1 = dt * f_batch[:, i-1, :]
            k2 = dt * f_batch[:, i-1, :]
            k3 = dt * f_batch[:, i-1, :]
            k4 = dt * f_batch[:, i-1, :]
            
            y_current = y_current + (k1 + 2*k2 + 2*k3 + k4) / 6.0
            y_result[:, i, :] = y_current
        
        return y_result
    
    def trapezoid_integration_batch(self, 
                                  y_batch: np.ndarray,
                                  x_batch: np.ndarray) -> np.ndarray:
        """
        批量梯形积分
        
        Args:
            y_batch: 函数值批次 [batch_size, n_points]
            x_batch: 自变量批次 [batch_size, n_points]
            
        Returns:
            integrals: 积分结果 [batch_size]
        """
        if not self.is_available:
            return self._trapz_cpu_fallback(y_batch, x_batch)
        
        try:
            if self.backend == "torch":
                return self._trapz_torch(y_batch, x_batch)
            elif self.backend == "cupy":
                return self._trapz_cupy(y_batch, x_batch)
            else:
                return self._trapz_cpu_fallback(y_batch, x_batch)
        except Exception as e:
            warnings.warn(f"GPU梯形积分失败，回退到CPU: {e}")
            return self._trapz_cpu_fallback(y_batch, x_batch)
    
    def _trapz_torch(self, y_batch, x_batch):
        """使用PyTorch进行梯形积分"""
        y = torch.from_numpy(y_batch).float().to(self.device)
        x = torch.from_numpy(x_batch).float().to(self.device)
        
        # 计算dx
        dx = x[:, 1:] - x[:, :-1]  # [batch_size, n_points-1]
        
        # 计算平均函数值
        y_avg = (y[:, 1:] + y[:, :-1]) / 2.0  # [batch_size, n_points-1]
        
        # 梯形积分
        integrals = torch.sum(y_avg * dx, dim=1)  # [batch_size]
        
        return integrals.cpu().numpy()
    
    def _trapz_cupy(self, y_batch, x_batch):
        """使用CuPy进行梯形积分"""
        y = cp.asarray(y_batch, dtype=cp.float32)
        x = cp.asarray(x_batch, dtype=cp.float32)
        
        # 计算dx
        dx = x[:, 1:] - x[:, :-1]
        
        # 计算平均函数值
        y_avg = (y[:, 1:] + y[:, :-1]) / 2.0
        
        # 梯形积分
        integrals = cp.sum(y_avg * dx, axis=1)
        
        return cp.asnumpy(integrals)
    
    def _trapz_cpu_fallback(self, y_batch, x_batch):
        """CPU回退梯形积分"""
        batch_size = y_batch.shape[0]
        integrals = np.zeros(batch_size)
        
        for i in range(batch_size):
            integrals[i] = np.trapz(y_batch[i], x_batch[i])
        
        return integrals
    
    def simpson_integration_batch(self, 
                                y_batch: np.ndarray,
                                x_batch: np.ndarray) -> np.ndarray:
        """
        批量Simpson积分
        
        Args:
            y_batch: 函数值批次 [batch_size, n_points] (n_points必须为奇数)
            x_batch: 自变量批次 [batch_size, n_points]
            
        Returns:
            integrals: 积分结果 [batch_size]
        """
        if not self.is_available:
            return self._simpson_cpu_fallback(y_batch, x_batch)
        
        try:
            if self.backend == "torch":
                return self._simpson_torch(y_batch, x_batch)
            elif self.backend == "cupy":
                return self._simpson_cupy(y_batch, x_batch)
            else:
                return self._simpson_cpu_fallback(y_batch, x_batch)
        except Exception as e:
            warnings.warn(f"GPU Simpson积分失败，回退到CPU: {e}")
            return self._simpson_cpu_fallback(y_batch, x_batch)
    
    def _simpson_torch(self, y_batch, x_batch):
        """使用PyTorch进行Simpson积分"""
        y = torch.from_numpy(y_batch).float().to(self.device)
        x = torch.from_numpy(x_batch).float().to(self.device)
        
        n_points = y.shape[1]
        if n_points % 2 == 0:
            # 如果点数为偶数，去掉最后一个点
            y = y[:, :-1]
            x = x[:, :-1]
            n_points -= 1
        
        # Simpson公式权重：1, 4, 2, 4, 2, ..., 4, 1
        weights = torch.ones(n_points, device=self.device)
        weights[1::2] = 4  # 奇数索引
        weights[2::2] = 2  # 偶数索引（除了首尾）
        weights[0] = 1
        weights[-1] = 1
        
        # 计算步长（假设等间距）
        h = (x[:, -1] - x[:, 0]) / (n_points - 1)
        
        # Simpson积分
        integrals = h * torch.sum(y * weights, dim=1) / 3.0
        
        return integrals.cpu().numpy()
    
    def _simpson_cupy(self, y_batch, x_batch):
        """使用CuPy进行Simpson积分"""
        y = cp.asarray(y_batch, dtype=cp.float32)
        x = cp.asarray(x_batch, dtype=cp.float32)
        
        n_points = y.shape[1]
        if n_points % 2 == 0:
            y = y[:, :-1]
            x = x[:, :-1]
            n_points -= 1
        
        # Simpson公式权重
        weights = cp.ones(n_points)
        weights[1::2] = 4
        weights[2::2] = 2
        weights[0] = 1
        weights[-1] = 1
        
        # 计算步长
        h = (x[:, -1] - x[:, 0]) / (n_points - 1)
        
        # Simpson积分
        integrals = h * cp.sum(y * weights, axis=1) / 3.0
        
        return cp.asnumpy(integrals)
    
    def _simpson_cpu_fallback(self, y_batch, x_batch):
        """CPU回退Simpson积分"""
        from scipy.integrate import simpson
        
        batch_size = y_batch.shape[0]
        integrals = np.zeros(batch_size)
        
        for i in range(batch_size):
            try:
                integrals[i] = simpson(y_batch[i], x_batch[i])
            except Exception:
                # 如果scipy不可用，使用简化的Simpson公式
                y = y_batch[i]
                x = x_batch[i]
                n = len(y)
                if n % 2 == 0:
                    y = y[:-1]
                    x = x[:-1]
                    n -= 1
                
                h = (x[-1] - x[0]) / (n - 1)
                weights = np.ones(n)
                weights[1::2] = 4
                weights[2::2] = 2
                weights[0] = 1
                weights[-1] = 1
                
                integrals[i] = h * np.sum(y * weights) / 3.0
        
        return integrals


def create_gpu_integration(backend: str = "auto", device_id: int = 0) -> GPUIntegration:
    """
    创建GPU数值积分器的工厂函数
    
    Args:
        backend: 计算后端
        device_id: GPU设备ID
        
    Returns:
        GPUIntegration实例
    """
    return GPUIntegration(backend=backend, device_id=device_id)
