"""
BPM求解器实现
===========

<PERSON><PERSON><PERSON><PERSON><PERSON>宽带噪声模型
包括湍流边界层-后缘噪声、分离流噪声、桨尖涡噪声等
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from scipy import signal
from ..base import AcousticSolverBase
from ...interfaces.solver_interface import SolverConfig
from ...interfaces.data_interface import AerodynamicData

class BPMSolver(AcousticSolverBase):
    """Brooks<PERSON><PERSON><PERSON><PERSON>宽带噪声求解器 - 重构版本"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        
        # BPM特定参数
        self.kinematic_viscosity = config.solver_specific_params.get('kinematic_viscosity', 1.81e-5 / self.air_density)
        self.turbulence_intensity = config.solver_specific_params.get('turbulence_intensity', 0.05)
        
        # 噪声机制开关
        self.enable_tbl_te_noise = config.solver_specific_params.get('enable_tbl_te_noise', True)
        self.enable_separation_noise = config.solver_specific_params.get('enable_separation_noise', True)
        self.enable_tip_vortex_noise = config.solver_specific_params.get('enable_tip_vortex_noise', True)
        self.enable_blunt_te_noise = config.solver_specific_params.get('enable_blunt_te_noise', True)
        
        # 边界层参数
        self.enable_dynamic_boundary_layer = config.solver_specific_params.get('enable_dynamic_boundary_layer', True)
        
        print(f"✅ BPM求解器初始化完成")
        print(f"   湍流度: {self.turbulence_intensity:.3f}")
        print(f"   动态边界层: {'启用' if self.enable_dynamic_boundary_layer else '禁用'}")
    
    @property
    def solver_name(self) -> str:
        return "BPM"
    
    def _setup_acoustic_sources(self, geometry_data: Dict[str, Any]) -> None:
        """设置声学源"""
        self.blade_count = geometry_data.get('blade_count', 3)
        self.rotor_radius = geometry_data.get('rotor_radius', 1.0)
        self.chord_distribution = geometry_data.get('chord_distribution', np.ones(10) * 0.1)
        
        # 径向站位
        self.radial_stations = np.linspace(0.1 * self.rotor_radius, self.rotor_radius, len(self.chord_distribution))
    
    def _compute_acoustic_pressure(self, aero_data: AerodynamicData, 
                                 observer_position: np.ndarray) -> float:
        """计算宽带噪声声压"""
        total_pressure_squared = 0.0
        
        # 对每个径向站位计算宽带噪声
        for i, r_station in enumerate(self.radial_stations):
            # 获取局部气动参数
            local_velocity = self._get_local_velocity(aero_data, r_station)
            local_alpha = self._get_local_angle_of_attack(aero_data, r_station)
            local_chord = self.chord_distribution[i]
            
            # 计算宽带噪声功率谱密度
            frequencies, psd = self._calculate_broadband_noise_psd(
                local_velocity, local_alpha, r_station, local_chord
            )
            
            # 计算观测点的声压贡献
            distance = np.linalg.norm(observer_position - self._get_source_position(r_station, aero_data.time_stamp))
            pressure_contribution = self._convert_psd_to_pressure(psd, frequencies, distance)
            
            total_pressure_squared += pressure_contribution**2
        
        # 非相干叠加
        return np.sqrt(total_pressure_squared)
    
    def _get_local_velocity(self, aero_data: AerodynamicData, r_station: float) -> float:
        """获取局部相对速度"""
        # 简化：假设主要是切向速度
        if hasattr(aero_data, 'velocity_field') and aero_data.velocity_field is not None:
            # 插值获取该径向位置的速度
            velocity_mag = np.linalg.norm(aero_data.velocity_field[0])  # 简化取第一个点
        else:
            # 估算切向速度
            omega = 2 * np.pi * 25  # 假设转速 1500 RPM
            velocity_mag = omega * r_station
        
        return velocity_mag
    
    def _get_local_angle_of_attack(self, aero_data: AerodynamicData, r_station: float) -> float:
        """获取局部攻角"""
        # 简化的攻角估算
        return np.radians(5.0)  # 假设5度攻角
    
    def _get_source_position(self, r_station: float, time: float) -> np.ndarray:
        """获取源位置"""
        # 简化：假设源在转子盘面上
        angle = 0.0  # 简化为固定角度
        x = r_station * np.cos(angle)
        y = r_station * np.sin(angle)
        z = 0.0
        return np.array([x, y, z])
    
    def _calculate_broadband_noise_psd(self, velocity: float, alpha: float, 
                                     radius: float, chord: float) -> Tuple[np.ndarray, np.ndarray]:
        """计算宽带噪声功率谱密度"""
        # 频率数组
        frequencies = np.logspace(np.log10(self.frequency_min), np.log10(self.frequency_max), self.frequency_points)
        
        # 计算边界层参数
        bl_params = self._calculate_boundary_layer_parameters(velocity, alpha, chord)
        
        # 初始化各噪声分量
        psd_total = np.zeros_like(frequencies)
        
        # 湍流边界层-后缘噪声
        if self.enable_tbl_te_noise:
            psd_tbl_te = self._calculate_tbl_te_noise(frequencies, velocity, bl_params)
            psd_total += psd_tbl_te
        
        # 分离流噪声
        if self.enable_separation_noise:
            psd_separation = self._calculate_separation_noise(frequencies, velocity, alpha, bl_params)
            psd_total += psd_separation
        
        # 桨尖涡噪声
        if self.enable_tip_vortex_noise and radius > 0.8 * self.rotor_radius:
            psd_tip_vortex = self._calculate_tip_vortex_noise(frequencies, velocity, alpha)
            psd_total += psd_tip_vortex
        
        # 钝后缘噪声
        if self.enable_blunt_te_noise:
            psd_blunt_te = self._calculate_blunt_te_noise(frequencies, velocity, bl_params)
            psd_total += psd_blunt_te
        
        return frequencies, psd_total
    
    def _calculate_boundary_layer_parameters(self, velocity: float, alpha: float, chord: float) -> Dict:
        """计算边界层参数"""
        # 雷诺数
        Re_c = velocity * chord / self.kinematic_viscosity
        
        if self.enable_dynamic_boundary_layer:
            # 动态边界层参数
            alpha_deg = abs(alpha) * 180 / np.pi
            
            # 考虑攻角影响的边界层厚度
            if alpha_deg > 5.0:
                separation_factor = 1.0 + 0.1 * (alpha_deg - 5.0)
            else:
                separation_factor = 1.0
            
            delta_star_p = 0.0463 * chord * Re_c**(-0.2)
            delta_star_s = 0.0463 * chord * Re_c**(-0.2) * separation_factor
        else:
            # 静态边界层参数
            delta_star_p = 0.0463 * chord * Re_c**(-0.2)
            delta_star_s = 0.0463 * chord * Re_c**(-0.2)
        
        return {
            'Re_c': Re_c,
            'delta_star_p': delta_star_p,
            'delta_star_s': delta_star_s,
            'alpha': alpha
        }
    
    def _calculate_tbl_te_noise(self, frequencies: np.ndarray, velocity: float, bl_params: Dict) -> np.ndarray:
        """
        计算湍流边界层-后缘噪声（基于adevice_complement5.md规范）

        完整的BPM TBL-TE噪声模型实现
        """
        delta_star_p = bl_params['delta_star_p']
        delta_star_s = bl_params['delta_star_s']
        Re_c = bl_params['Re_c']
        alpha = bl_params['alpha']

        # Strouhal数
        St_p = frequencies * delta_star_p / velocity
        St_s = frequencies * delta_star_s / velocity

        # 雷诺数修正
        Re_delta_p = velocity * delta_star_p / self.kinematic_viscosity
        Re_delta_s = velocity * delta_star_s / self.kinematic_viscosity

        # 压力面贡献（完整BPM公式）
        A_p = 10 * np.log10((delta_star_p * velocity**5) / (frequencies * self.sound_speed**2))

        # 改进的谱形状函数
        B_p = np.zeros_like(frequencies)
        for i, st in enumerate(St_p):
            if st <= 0.1:
                B_p[i] = 10 * np.log10((st / 0.1)**2)
            elif st <= 1.0:
                B_p[i] = 0.0
            else:
                B_p[i] = 10 * np.log10((st)**(-2.5))

        # 攻角修正
        alpha_deg = abs(alpha) * 180 / np.pi
        if alpha_deg > 1.33:
            K_alpha = 10**(0.1 * (alpha_deg - 1.33))
        else:
            K_alpha = 1.0

        psd_p = 10**((A_p + B_p) / 10) * K_alpha

        # 吸力面贡献（包含分离效应）
        A_s = 10 * np.log10((delta_star_s * velocity**5) / (frequencies * self.sound_speed**2))

        B_s = np.zeros_like(frequencies)
        for i, st in enumerate(St_s):
            if st <= 0.1:
                B_s[i] = 10 * np.log10((st / 0.1)**2)
            elif st <= 1.0:
                B_s[i] = 0.0
            else:
                B_s[i] = 10 * np.log10((st)**(-2.5))

        # 分离修正（吸力面）
        if alpha_deg > 5.0:
            separation_enhancement = 1.0 + 0.2 * (alpha_deg - 5.0)
        else:
            separation_enhancement = 1.0

        psd_s = 10**((A_s + B_s) / 10) * K_alpha * separation_enhancement

        return psd_p + psd_s
    
    def _calculate_separation_noise(self, frequencies: np.ndarray, velocity: float, 
                                  alpha: float, bl_params: Dict) -> np.ndarray:
        """计算分离流噪声"""
        alpha_deg = abs(alpha) * 180 / np.pi
        
        if alpha_deg < 5.0:
            return np.zeros_like(frequencies)
        
        # 分离流噪声经验公式
        delta_star_s = bl_params['delta_star_s']
        St_s = frequencies * delta_star_s / velocity
        
        # 分离流增强因子
        separation_factor = 1.0 + 0.2 * (alpha_deg - 5.0)
        
        A_sep = 10 * np.log10((delta_star_s * velocity**5 * separation_factor) / (frequencies * self.sound_speed**2))
        B_sep = 10 * np.log10(St_s**2 / ((St_s**2 + 0.1) * (St_s**2 + 0.01)))
        
        return 10**((A_sep + B_sep) / 10)
    
    def _calculate_tip_vortex_noise(self, frequencies: np.ndarray, velocity: float, alpha: float) -> np.ndarray:
        """计算桨尖涡噪声"""
        alpha_deg = abs(alpha) * 180 / np.pi
        
        # 桨尖涡强度
        circulation = 0.5 * velocity * 0.1 * alpha  # 简化估算
        
        # 桨尖涡噪声经验公式
        A_tip = 10 * np.log10((circulation**2 * velocity**2) / (frequencies * self.sound_speed**2))
        B_tip = -20 * np.log10(frequencies / 1000.0 + 1.0)  # 高频衰减
        
        return 10**((A_tip + B_tip) / 10)
    
    def _calculate_blunt_te_noise(self, frequencies: np.ndarray, velocity: float, bl_params: Dict) -> np.ndarray:
        """计算钝后缘噪声"""
        # 简化的钝后缘噪声模型
        te_thickness = 0.001  # 假设后缘厚度1mm
        
        St_h = frequencies * te_thickness / velocity
        
        A_blunt = 10 * np.log10((te_thickness * velocity**6) / (frequencies * self.sound_speed**2))
        B_blunt = 10 * np.log10(St_h**2 / ((St_h**2 + 0.25) * (St_h**2 + 0.0625)))
        
        return 10**((A_blunt + B_blunt) / 10)
    
    def _convert_psd_to_pressure(self, psd: np.ndarray, frequencies: np.ndarray, distance: float) -> float:
        """将功率谱密度转换为声压"""
        # 积分功率谱密度得到总声压
        df = frequencies[1] - frequencies[0] if len(frequencies) > 1 else 1.0
        total_power = np.sum(psd) * df
        
        # 考虑距离衰减
        pressure_squared = total_power / (4 * np.pi * distance**2)
        
        return np.sqrt(pressure_squared)
    
    def _compute_frequency_spectrum(self, time_series: np.ndarray, 
                                  time_array: np.ndarray) -> tuple:
        """计算频谱"""
        # BPM模型直接计算频谱，不需要时域转换
        frequencies = np.logspace(np.log10(self.frequency_min), np.log10(self.frequency_max), self.frequency_points)
        
        # 使用最后一个时间点的数据计算频谱
        if len(time_series) > 0:
            # 简化：返回基于最后时刻的频谱
            spectrum = np.ones_like(frequencies) * time_series[-1]**2
        else:
            spectrum = np.zeros_like(frequencies)
        
        return frequencies, spectrum
