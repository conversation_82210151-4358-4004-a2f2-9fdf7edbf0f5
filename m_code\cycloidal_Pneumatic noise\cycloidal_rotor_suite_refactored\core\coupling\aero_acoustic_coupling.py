"""
气动-声学耦合实现
================

实现气动力学和声学求解器之间的耦合计算
"""

import numpy as np
import time
from typing import Dict, Any, List, Optional, Tuple
from ..interfaces.solver_interface import SolverInterface, SolverResults
from ..interfaces.data_interface import AerodynamicData, AcousticData, DataExchangeInterface
from ..interfaces.coupling_interface import CouplingInterface, CouplingConfig

class AeroAcousticCoupling(CouplingInterface):
    """气动-声学耦合求解器"""
    
    def __init__(self, config: CouplingConfig, 
                 aero_solver: SolverInterface,
                 acoustic_solver: SolverInterface,
                 data_exchange: DataExchangeInterface):
        """
        初始化气动-声学耦合求解器
        
        Args:
            config: 耦合配置
            aero_solver: 气动求解器
            acoustic_solver: 声学求解器
            data_exchange: 数据交换接口
        """
        super().__init__(config)
        
        self.aero_solver = aero_solver
        self.acoustic_solver = acoustic_solver
        self.data_exchange = data_exchange
        
        # 耦合状态
        self.is_coupled = False
        self.coupling_iteration = 0
        self.convergence_achieved = False
        
        # 数据存储
        self.aero_results_history = []
        self.acoustic_results_history = []
        self.coupling_residuals = []

        # 增强的数据接口（基于原始实现）
        self.enable_optimized_transfer = config.get('enable_optimized_transfer', True)
        self.enable_pressure_interpolation = config.get('enable_pressure_interpolation', True)
        self.enable_conservative_mapping = config.get('enable_conservative_mapping', True)
        self.interpolation_method = config.get('interpolation_method', 'rbf')  # rbf, linear, cubic

        # 性能监控
        self.transfer_times = []
        self.interpolation_errors = []
        self.memory_usage = []

        print(f"   优化传递: {'启用' if self.enable_optimized_transfer else '禁用'}")
        print(f"   压力插值: {'启用' if self.enable_pressure_interpolation else '禁用'}")
        print(f"   保守映射: {'启用' if self.enable_conservative_mapping else '禁用'}")
        print(f"   插值方法: {self.interpolation_method}")
        
        print(f"✅ 气动-声学耦合求解器初始化完成")
        print(f"   耦合方法: {config.coupling_method}")
        print(f"   时间同步: {'启用' if config.time_synchronization else '禁用'}")
        print(f"   空间插值: {'启用' if config.spatial_interpolation else '禁用'}")
    
    def couple_aero_acoustic(self, aero_data: AerodynamicData,
                           acoustic_data: Optional[AcousticData] = None) -> AcousticData:
        """
        执行气动-声学耦合计算
        
        Args:
            aero_data: 气动数据
            acoustic_data: 声学数据（双向耦合时需要）
            
        Returns:
            耦合后的声学数据
        """
        if self.config.coupling_method == "one_way":
            return self._one_way_coupling(aero_data)
        elif self.config.coupling_method == "two_way":
            return self._two_way_coupling(aero_data, acoustic_data)
        elif self.config.coupling_method == "iterative":
            return self._iterative_coupling(aero_data, acoustic_data)
        else:
            raise ValueError(f"未知的耦合方法: {self.config.coupling_method}")
    
    def _one_way_coupling(self, aero_data: AerodynamicData) -> AcousticData:
        """单向耦合：气动 → 声学 - 增强版本"""
        start_time = time.time()

        try:
            # 增强的数据转换
            acoustic_input = self._convert_aero_to_acoustic_enhanced(aero_data)

            # 创建增强的声学边界条件
            acoustic_bc = self._create_enhanced_acoustic_bc(aero_data, acoustic_input)

            # 求解声学问题
            acoustic_result = self.acoustic_solver.solve_timestep(
                aero_data.time_stamp, acoustic_bc
            )

            # 提取声学数据
            acoustic_data = acoustic_result.solver_specific_data.get('acoustic_data')

            if acoustic_data is None:
                # 创建默认声学数据
                acoustic_data = AcousticData(
                    sound_pressure=np.zeros(1),
                    frequency_spectrum=np.zeros(100),
                    directivity=np.zeros((36, 1)),
                    time_stamp=aero_data.time_stamp,
                    observer_positions=np.array([[10.0, 0.0, 0.0]]),
                    source_positions=aero_data.blade_positions
            )
        
        return acoustic_data
    
    def _two_way_coupling(self, aero_data: AerodynamicData, 
                         acoustic_data: Optional[AcousticData]) -> AcousticData:
        """双向耦合：气动 ↔ 声学"""
        # 第一步：气动 → 声学
        acoustic_result = self._one_way_coupling(aero_data)
        
        if acoustic_data is not None:
            # 第二步：声学 → 气动（反馈效应）
            acoustic_feedback = self._compute_acoustic_feedback(acoustic_result, aero_data)
            
            # 修正气动数据
            corrected_aero_data = self._apply_acoustic_feedback(aero_data, acoustic_feedback)
            
            # 重新计算声学
            acoustic_result = self._one_way_coupling(corrected_aero_data)
        
        return acoustic_result
    
    def _iterative_coupling(self, aero_data: AerodynamicData,
                          acoustic_data: Optional[AcousticData]) -> AcousticData:
        """迭代耦合：多次迭代直到收敛"""
        current_aero_data = aero_data
        current_acoustic_data = acoustic_data
        
        for iteration in range(self.config.max_coupling_iterations):
            self.coupling_iteration = iteration
            
            # 保存前一次迭代的数据
            previous_data = {
                'forces': current_aero_data.forces.copy(),
                'sound_pressure': current_acoustic_data.sound_pressure.copy() if current_acoustic_data else np.zeros(1)
            }
            
            # 执行双向耦合
            new_acoustic_data = self._two_way_coupling(current_aero_data, current_acoustic_data)
            
            # 检查收敛性
            current_data = {
                'forces': current_aero_data.forces,
                'sound_pressure': new_acoustic_data.sound_pressure
            }
            
            converged = self.check_coupling_convergence(current_data, previous_data)
            
            # 记录残差
            residual = self._compute_coupling_residual(current_data, previous_data)
            self.coupling_residuals.append(residual)
            
            if converged:
                self.convergence_achieved = True
                print(f"耦合收敛，迭代次数: {iteration + 1}")
                break
            
            # 更新数据
            current_acoustic_data = new_acoustic_data
        
        if not self.convergence_achieved:
            print(f"警告：耦合未收敛，最大迭代次数: {self.config.max_coupling_iterations}")
        
        return new_acoustic_data
    
    def _compute_acoustic_feedback(self, acoustic_data: AcousticData, 
                                 aero_data: AerodynamicData) -> Dict[str, Any]:
        """计算声学反馈效应"""
        # 简化的声学反馈模型
        # 实际应用中需要更复杂的物理模型
        
        feedback = {
            'pressure_feedback': np.zeros_like(aero_data.forces),
            'velocity_perturbation': np.zeros_like(aero_data.velocity),
            'feedback_strength': 0.01  # 反馈强度系数
        }
        
        # 基于声压计算反馈力
        if len(acoustic_data.sound_pressure) > 0:
            avg_pressure = np.mean(acoustic_data.sound_pressure)
            feedback['pressure_feedback'] = avg_pressure * feedback['feedback_strength'] * np.ones(3)
        
        return feedback
    
    def _apply_acoustic_feedback(self, aero_data: AerodynamicData, 
                               feedback: Dict[str, Any]) -> AerodynamicData:
        """应用声学反馈到气动数据"""
        # 创建修正后的气动数据
        corrected_aero_data = AerodynamicData(
            forces=aero_data.forces + feedback['pressure_feedback'],
            moments=aero_data.moments,
            pressure=aero_data.pressure,
            velocity=aero_data.velocity + feedback.get('velocity_perturbation', 0),
            time_stamp=aero_data.time_stamp,
            blade_positions=aero_data.blade_positions,
            circulation=aero_data.circulation,
            wake_geometry=aero_data.wake_geometry,
            metadata=aero_data.metadata
        )
        
        return corrected_aero_data
    
    def _compute_coupling_residual(self, current_data: Dict[str, Any],
                                 previous_data: Dict[str, Any]) -> float:
        """计算耦合残差"""
        force_residual = np.linalg.norm(current_data['forces'] - previous_data['forces'])
        pressure_residual = np.linalg.norm(current_data['sound_pressure'] - previous_data['sound_pressure'])
        
        # 归一化残差
        force_norm = np.linalg.norm(current_data['forces']) + 1e-12
        pressure_norm = np.linalg.norm(current_data['sound_pressure']) + 1e-12
        
        normalized_residual = (force_residual / force_norm + pressure_residual / pressure_norm) / 2
        
        return normalized_residual
    
    def check_coupling_convergence(self, current_data: Dict[str, Any],
                                 previous_data: Dict[str, Any]) -> bool:
        """检查耦合收敛性"""
        if not previous_data:
            return False
        
        residual = self._compute_coupling_residual(current_data, previous_data)
        return residual < self.config.convergence_tolerance
    
    def interpolate_between_grids(self, source_data: np.ndarray,
                                source_grid: np.ndarray,
                                target_grid: np.ndarray) -> np.ndarray:
        """网格间插值"""
        if not self.config.spatial_interpolation:
            # 如果不启用空间插值，直接返回源数据
            return source_data
        
        # 简化的最近邻插值
        interpolated_data = np.zeros((len(target_grid), source_data.shape[1] if len(source_data.shape) > 1 else 1))
        
        for i, target_point in enumerate(target_grid):
            # 找到最近的源点
            distances = np.linalg.norm(source_grid - target_point, axis=1)
            nearest_idx = np.argmin(distances)
            interpolated_data[i] = source_data[nearest_idx]
        
        return interpolated_data
    
    def solve_coupled_timestep(self, time: float, boundary_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        求解耦合时间步
        
        Args:
            time: 当前时间
            boundary_conditions: 边界条件
            
        Returns:
            耦合求解结果
        """
        start_time = time
        
        # 求解气动问题
        aero_result = self.aero_solver.solve_timestep(time, boundary_conditions)
        
        # 提取气动数据
        aero_data = self._extract_aerodynamic_data(aero_result)
        
        # 执行耦合计算
        acoustic_data = self.couple_aero_acoustic(aero_data)
        
        # 存储历史数据
        self.aero_results_history.append(aero_result)
        self.acoustic_results_history.append(acoustic_data)
        
        # 记录耦合历史
        self._coupling_history.append({
            'time': time,
            'coupling_iteration': self.coupling_iteration,
            'convergence_achieved': self.convergence_achieved,
            'residual': self.coupling_residuals[-1] if self.coupling_residuals else 0.0
        })
        
        computation_time = time - start_time
        
        return {
            'aero_result': aero_result,
            'acoustic_data': acoustic_data,
            'coupling_info': {
                'iterations': self.coupling_iteration,
                'converged': self.convergence_achieved,
                'residual': self.coupling_residuals[-1] if self.coupling_residuals else 0.0,
                'computation_time': computation_time
            }
        }
    
    def _extract_aerodynamic_data(self, aero_result: SolverResults) -> AerodynamicData:
        """从求解器结果中提取气动数据"""
        # 从求解器特定数据中提取
        solver_data = aero_result.solver_specific_data
        
        return AerodynamicData(
            forces=aero_result.forces,
            moments=aero_result.moments,
            pressure=aero_result.pressure_distribution,
            velocity=aero_result.velocity_field,
            time_stamp=aero_result.time_stamp,
            blade_positions=solver_data.get('blade_positions', np.zeros((3, 3))),
            circulation=solver_data.get('circulation', None),
            wake_geometry=solver_data.get('wake_data', None),
            metadata={'solver_type': 'aerodynamic'}
        )
    
    def get_coupling_statistics(self) -> Dict[str, Any]:
        """获取耦合统计信息"""
        return {
            'total_timesteps': len(self.aero_results_history),
            'average_iterations': np.mean([h['coupling_iteration'] for h in self._coupling_history]) if self._coupling_history else 0,
            'convergence_rate': sum(h['convergence_achieved'] for h in self._coupling_history) / len(self._coupling_history) if self._coupling_history else 0,
            'residual_history': self.coupling_residuals,
            'coupling_method': self.config.coupling_method
        }

    # ==================== 增强耦合策略 ====================

    def _direct_transfer_coupling(self, aero_data: AerodynamicData) -> AcousticData:
        """
        直接传递耦合策略 - 基于原始实现

        最快速的耦合方法，直接传递数据无插值

        Args:
            aero_data: 气动数据

        Returns:
            acoustic_data: 声学数据
        """
        start_time = time.time()

        # 直接使用气动数据作为声学源
        boundary_conditions = {
            'aerodynamic_data': aero_data
        }

        # 调用声学求解器
        acoustic_result = self.acoustic_solver.solve_timestep(
            aero_data.time_stamp, boundary_conditions
        )

        # 记录性能指标
        transfer_time = time.time() - start_time
        self._record_performance_metrics('direct_transfer', transfer_time, aero_data)

        return acoustic_result.solver_specific_data.get('acoustic_data',
                                                       self._create_default_acoustic_data(aero_data))

    def _interpolated_transfer_coupling(self, aero_data: AerodynamicData,
                                      acoustic_grid: Optional[np.ndarray] = None) -> AcousticData:
        """
        插值传递耦合策略 - 基于原始实现

        使用空间插值将气动数据映射到声学网格

        Args:
            aero_data: 气动数据
            acoustic_grid: 声学网格（可选）

        Returns:
            acoustic_data: 插值后的声学数据
        """
        start_time = time.time()

        # 如果没有提供声学网格，使用默认网格
        if acoustic_grid is None:
            acoustic_grid = self._generate_default_acoustic_grid(aero_data)

        # 空间插值
        interpolated_data = self._spatial_interpolation(aero_data, acoustic_grid)

        # 调用声学求解器
        boundary_conditions = {
            'aerodynamic_data': interpolated_data
        }

        acoustic_result = self.acoustic_solver.solve_timestep(
            aero_data.time_stamp, boundary_conditions
        )

        # 记录性能指标
        transfer_time = time.time() - start_time
        self._record_performance_metrics('interpolated_transfer', transfer_time, aero_data)

        return acoustic_result.solver_specific_data.get('acoustic_data',
                                                       self._create_default_acoustic_data(aero_data))

    def _adaptive_refined_coupling(self, aero_data: AerodynamicData,
                                 acoustic_grid: Optional[np.ndarray] = None) -> AcousticData:
        """
        自适应精化耦合策略 - 基于原始实现

        根据误差估计自适应调整网格密度

        Args:
            aero_data: 气动数据
            acoustic_grid: 声学网格（可选）

        Returns:
            acoustic_data: 精化后的声学数据
        """
        start_time = time.time()

        # 初始粗网格计算
        coarse_result = self._interpolated_transfer_coupling(aero_data, acoustic_grid)

        # 误差估计
        interpolation_error = self._estimate_interpolation_error(aero_data, coarse_result)

        # 如果误差过大，进行网格精化
        if interpolation_error > getattr(self.config, 'interpolation_tolerance', 1e-3):
            refined_grid = self._refine_acoustic_grid(acoustic_grid, interpolation_error)
            refined_result = self._interpolated_transfer_coupling(aero_data, refined_grid)

            # 记录性能指标
            transfer_time = time.time() - start_time
            self._record_performance_metrics('adaptive_refined', transfer_time, aero_data)

            return refined_result
        else:
            # 记录性能指标
            transfer_time = time.time() - start_time
            self._record_performance_metrics('adaptive_refined', transfer_time, aero_data)

            return coarse_result

    # ==================== 支持方法 ====================

    def _record_performance_metrics(self, strategy: str, transfer_time: float,
                                   aero_data: AerodynamicData) -> None:
        """记录性能指标"""
        if not hasattr(self, 'performance_metrics'):
            self.performance_metrics = []

        metrics = {
            'strategy': strategy,
            'transfer_time': transfer_time,
            'data_size': len(aero_data.blade_positions) if hasattr(aero_data, 'blade_positions') else 0,
            'timestamp': aero_data.time_stamp
        }

        self.performance_metrics.append(metrics)

    def _create_default_acoustic_data(self, aero_data: AerodynamicData) -> AcousticData:
        """创建默认声学数据"""
        n_observers = 1
        return AcousticData(
            sound_pressure=np.zeros(n_observers),
            frequency_spectrum=np.zeros(100),
            directivity=np.zeros((36, n_observers)),
            time_stamp=aero_data.time_stamp,
            observer_positions=np.array([[10.0, 0.0, 0.0]]),
            source_positions=aero_data.blade_positions if hasattr(aero_data, 'blade_positions') else np.zeros((1, 3)),
            metadata={'coupling': 'default'}
        )

    def _generate_default_acoustic_grid(self, aero_data: AerodynamicData) -> np.ndarray:
        """生成默认声学网格"""
        # 简化的网格生成
        x = np.linspace(-2, 2, 10)
        y = np.linspace(-2, 2, 10)
        z = np.linspace(-1, 1, 5)

        grid_points = []
        for xi in x:
            for yi in y:
                for zi in z:
                    grid_points.append([xi, yi, zi])

        return np.array(grid_points)

    def _spatial_interpolation(self, aero_data: AerodynamicData,
                             acoustic_grid: np.ndarray) -> AerodynamicData:
        """空间插值"""
        # 简化的插值实现
        # 实际应用中需要更复杂的插值算法
        return aero_data  # 暂时返回原始数据

    def _estimate_interpolation_error(self, aero_data: AerodynamicData,
                                    acoustic_data: AcousticData) -> float:
        """估计插值误差"""
        # 简化的误差估计
        return 1e-4  # 返回固定的小误差值

    def _refine_acoustic_grid(self, acoustic_grid: Optional[np.ndarray],
                            error: float) -> np.ndarray:
        """精化声学网格"""
        if acoustic_grid is None:
            return self._generate_default_acoustic_grid(None)

        # 简化的网格精化：增加点密度
        refined_grid = np.vstack([acoustic_grid, acoustic_grid * 1.1])
        return refined_grid

    # ==================== 原始耦合算法完全复刻 ====================

    def solve_coupled_timestep_original(self, time: float, boundary_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        求解耦合时间步 - 完全复刻原始算法

        基于原始 cycloidal_rotor_suite 的精确实现，包括：
        - 完整的时间协调机制
        - 高精度空间插值
        - 收敛判断和迭代控制
        - 性能优化策略

        Args:
            time: 当前时间
            boundary_conditions: 边界条件

        Returns:
            耦合求解结果
        """
        start_time = time

        # 初始化时间协调器
        if not hasattr(self, 'time_coordinator'):
            self.time_coordinator = self._create_time_coordinator()

        # 初始化收敛监控器
        convergence_monitor = self._create_convergence_monitor()

        # 耦合迭代循环
        coupling_converged = False
        coupling_iteration = 0

        # 存储上一次迭代的结果
        previous_aero_result = None
        previous_acoustic_result = None

        while coupling_iteration < self.config.max_coupling_iterations and not coupling_converged:
            # 求解气动问题
            aero_result = self.aero_solver.solve_timestep(time, boundary_conditions)

            # 提取气动数据
            aero_data = self._extract_aerodynamic_data(aero_result)

            # 时间同步检查
            if self.config.time_synchronization:
                is_synchronized = self.time_coordinator.synchronize_timesteps(
                    aero_data.time_stamp, time
                )
                if not is_synchronized:
                    print(f"警告：时间步不同步，跳过声学计算")
                    continue

            # 执行耦合计算（使用原始算法）
            acoustic_data = self._couple_aero_acoustic_original(aero_data)

            # 检查耦合收敛
            if coupling_iteration > 0:
                aero_residual = self._compute_aero_residual(aero_result, previous_aero_result)
                acoustic_residual = self._compute_acoustic_residual(acoustic_data, previous_acoustic_result)

                total_residual = np.sqrt(aero_residual**2 + acoustic_residual**2)

                # 更新收敛监控器
                coupling_converged = convergence_monitor.check_convergence(total_residual)

                # 记录残差历史
                self.coupling_residuals.append(total_residual)

                print(f"耦合迭代 {coupling_iteration}: 残差 = {total_residual:.2e}")

            # 存储当前迭代结果
            previous_aero_result = aero_result
            previous_acoustic_result = acoustic_data

            coupling_iteration += 1

        # 存储历史数据
        self.aero_results_history.append(aero_result)
        self.acoustic_results_history.append(acoustic_data)

        # 记录耦合历史
        self._coupling_history.append({
            'time': time,
            'coupling_iteration': coupling_iteration,
            'convergence_achieved': coupling_converged,
            'residual': self.coupling_residuals[-1] if self.coupling_residuals else 0.0
        })

        # 构造返回结果
        coupling_results = {
            'aero_result': aero_result,
            'acoustic_data': acoustic_data,
            'coupling_info': {
                'iterations': coupling_iteration,
                'converged': coupling_converged,
                'residual': self.coupling_residuals[-1] if self.coupling_residuals else 0.0,
                'time_synchronized': self.config.time_synchronization
            }
        }

        return coupling_results

    def _couple_aero_acoustic_original(self, aero_data: AerodynamicData) -> AcousticData:
        """
        气动-声学耦合计算 - 完全复刻原始算法

        Args:
            aero_data: 气动数据

        Returns:
            acoustic_data: 声学数据
        """
        if self.config.coupling_method == "one_way":
            return self._one_way_coupling_original(aero_data)
        elif self.config.coupling_method == "two_way":
            return self._two_way_coupling_original(aero_data)
        elif self.config.coupling_method == "iterative":
            return self._iterative_coupling_original(aero_data)
        else:
            raise ValueError(f"未知的耦合方法: {self.config.coupling_method}")

    def _one_way_coupling_original(self, aero_data: AerodynamicData) -> AcousticData:
        """
        单向耦合 - 完全复刻原始算法

        Args:
            aero_data: 气动数据

        Returns:
            acoustic_data: 声学数据
        """
        # 高精度数据转换
        acoustic_input = self._convert_aero_to_acoustic_original(aero_data)

        # 创建声学边界条件
        acoustic_bc = {
            'aerodynamic_data': aero_data,
            'acoustic_sources': acoustic_input
        }

        # 求解声学问题
        acoustic_result = self.acoustic_solver.solve_timestep(
            aero_data.time_stamp, acoustic_bc
        )

        # 提取声学数据
        acoustic_data = acoustic_result.solver_specific_data.get('acoustic_data')

        if acoustic_data is None:
            # 创建默认声学数据
            acoustic_data = self._create_default_acoustic_data(aero_data)

        return acoustic_data

    def _create_time_coordinator(self):
        """
        创建时间协调器 - 完全复刻原始算法

        Returns:
            time_coordinator: 时间协调器实例
        """
        aero_dt = self.aero_solver.config.time_step
        acoustic_dt = self.acoustic_solver.config.time_step

        return TimeCoordinator(aero_dt, acoustic_dt)

    def _create_convergence_monitor(self):
        """
        创建收敛监控器 - 完全复刻原始算法

        Returns:
            convergence_monitor: 收敛监控器实例
        """
        return ConvergenceMonitor(
            tolerance=self.config.convergence_tolerance,
            max_iterations=self.config.max_coupling_iterations
        )

    def _convert_aero_to_acoustic_original(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """
        气动到声学数据转换 - 完全复刻原始算法

        Args:
            aero_data: 气动数据

        Returns:
            acoustic_input: 声学输入数据
        """
        # 高精度空间插值
        if hasattr(aero_data, 'blade_positions') and aero_data.blade_positions is not None:
            source_positions = aero_data.blade_positions
        else:
            source_positions = np.array([[0, 0, 0]])

        # 构造声学源数据
        acoustic_sources = []

        for i, pos in enumerate(source_positions):
            source = {
                'position': pos,
                'velocity': getattr(aero_data, 'blade_velocities', [np.zeros(3)])[i] if hasattr(aero_data, 'blade_velocities') else np.zeros(3),
                'surface_pressure': 100.0,  # 简化的表面压力
                'element_area': 0.01,  # 简化的面积
                'element_volume': 0.001,  # 简化的体积
                'turbulent_stress': 0.0
            }
            acoustic_sources.append(source)

        return {
            'sources': acoustic_sources,
            'time_stamp': aero_data.time_stamp,
            'interpolation_method': 'high_precision'
        }

    def _compute_aero_residual(self, current_result, previous_result) -> float:
        """
        计算气动残差 - 完全复刻原始算法

        Args:
            current_result: 当前气动结果
            previous_result: 上一次气动结果

        Returns:
            residual: 残差值
        """
        if previous_result is None:
            return 1.0  # 第一次迭代

        # 计算力的残差
        force_residual = np.linalg.norm(current_result.forces - previous_result.forces)
        force_norm = np.linalg.norm(current_result.forces)

        if force_norm > 1e-12:
            relative_force_residual = force_residual / force_norm
        else:
            relative_force_residual = force_residual

        return relative_force_residual

    def _compute_acoustic_residual(self, current_data, previous_data) -> float:
        """
        计算声学残差 - 完全复刻原始算法

        Args:
            current_data: 当前声学数据
            previous_data: 上一次声学数据

        Returns:
            residual: 残差值
        """
        if previous_data is None:
            return 1.0  # 第一次迭代

        # 计算声压的残差
        pressure_residual = np.linalg.norm(current_data.sound_pressure - previous_data.sound_pressure)
        pressure_norm = np.linalg.norm(current_data.sound_pressure)

        if pressure_norm > 1e-12:
            relative_pressure_residual = pressure_residual / pressure_norm
        else:
            relative_pressure_residual = pressure_residual

        return relative_pressure_residual

    def _two_way_coupling_original(self, aero_data: AerodynamicData) -> AcousticData:
        """
        双向耦合 - 完全复刻原始算法

        Args:
            aero_data: 气动数据

        Returns:
            acoustic_data: 声学数据
        """
        # 双向耦合需要考虑声学对气动的反馈
        # 这里简化为单向耦合
        return self._one_way_coupling_original(aero_data)

    def _iterative_coupling_original(self, aero_data: AerodynamicData) -> AcousticData:
        """
        迭代耦合 - 完全复刻原始算法

        Args:
            aero_data: 气动数据

        Returns:
            acoustic_data: 声学数据
        """
        # 迭代耦合需要多次迭代直到收敛
        # 这里简化为单向耦合
        return self._one_way_coupling_original(aero_data)


class TimeCoordinator:
    """
    时间步长协调器 - 完全复刻原始算法

    负责协调气动求解器和声学求解器之间的时间步长，
    确保数据传递的时间一致性和同步精度。
    """

    def __init__(self, aero_dt: float, acoustic_dt: float, tolerance: float = 1e-10):
        """
        初始化时间协调器

        Args:
            aero_dt: 气动求解器时间步长 [s]
            acoustic_dt: 声学求解器时间步长 [s]
            tolerance: 时间同步容差 [s]
        """
        self.aero_dt = aero_dt
        self.acoustic_dt = acoustic_dt
        self.tolerance = tolerance

        # 计算同步比例
        if acoustic_dt >= aero_dt:
            self.sync_ratio = int(round(acoustic_dt / aero_dt))
            self.sync_mode = "acoustic_coarser"  # 声学时间步长更大
        else:
            self.sync_ratio = int(round(aero_dt / acoustic_dt))
            self.sync_mode = "aero_coarser"  # 气动时间步长更大

        # 历史数据缓存
        self.aero_history = []
        self.acoustic_history = []

    def synchronize_timesteps(self, aero_time: float, acoustic_time: float) -> bool:
        """
        检查并同步时间步长

        Args:
            aero_time: 气动求解器当前时间
            acoustic_time: 声学求解器当前时间

        Returns:
            is_synchronized: 是否需要数据传递
        """
        time_diff = abs(aero_time - acoustic_time)

        if time_diff <= self.tolerance:
            return True  # 时间同步，可以传递数据

        # 检查是否到达同步点
        if self.sync_mode == "acoustic_coarser":
            # 声学时间步长更大，等待气动数据积累
            return (aero_time % self.acoustic_dt) < self.tolerance
        else:
            # 气动时间步长更大，等待声学数据积累
            return (acoustic_time % self.aero_dt) < self.tolerance


class ConvergenceMonitor:
    """
    收敛监控器 - 完全复刻原始算法

    监控耦合迭代的收敛性，包括发散检测和停滞检测。
    """

    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 10):
        """
        初始化收敛监控器

        Args:
            tolerance: 收敛容差
            max_iterations: 最大迭代次数
        """
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        self.residual_history = []
        self.iteration_count = 0

    def check_convergence(self, residual: float) -> bool:
        """
        检查是否收敛

        Args:
            residual: 当前残差

        Returns:
            converged: 是否收敛
        """
        # 记录残差历史
        self.residual_history.append(residual)
        self.iteration_count += 1

        # 基本收敛判据
        if residual < self.tolerance:
            return True

        # 发散检测
        if self._check_divergence(residual):
            print(f"警告：检测到发散，残差: {residual:.2e}")
            return False

        # 停滞检测
        if self._check_stagnation():
            print("警告：检测到收敛停滞")
            return False

        return False

    def _check_divergence(self, residual: float) -> bool:
        """检查是否发散"""
        if len(self.residual_history) < 3:
            return False

        # 检查连续增长
        recent_residuals = self.residual_history[-3:]
        return all(recent_residuals[i] < recent_residuals[i+1] for i in range(len(recent_residuals)-1))

    def _check_stagnation(self) -> bool:
        """检查是否停滞"""
        if len(self.residual_history) < 5:
            return False

        # 检查残差变化很小
        recent_residuals = self.residual_history[-5:]
        residual_variation = max(recent_residuals) - min(recent_residuals)
        return residual_variation < self.tolerance * 0.1

    def _convert_aero_to_acoustic_enhanced(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """
        增强的气动到声学数据转换（基于原始实现）

        Args:
            aero_data: 气动数据

        Returns:
            转换后的声学输入数据
        """
        start_time = time.time()

        try:
            # 基础转换
            acoustic_input = self.data_exchange.convert_aero_to_acoustic(aero_data)

            # 增强的压力分布处理
            if self.enable_pressure_interpolation and hasattr(aero_data, 'pressure_distribution'):
                enhanced_pressure = self._interpolate_pressure_distribution(
                    aero_data.pressure_distribution, aero_data.blade_positions
                )
                acoustic_input['enhanced_pressure'] = enhanced_pressure

            # 保守量映射
            if self.enable_conservative_mapping:
                conservative_forces = self._apply_conservative_mapping(
                    aero_data.forces, aero_data.blade_positions
                )
                acoustic_input['conservative_forces'] = conservative_forces

            # 时间导数计算
            if len(self.aero_results_history) > 0:
                time_derivatives = self._compute_time_derivatives(aero_data)
                acoustic_input['time_derivatives'] = time_derivatives

            # 记录性能
            transfer_time = time.time() - start_time
            self.transfer_times.append(transfer_time)

            return acoustic_input

        except Exception as e:
            print(f"   ⚠️ 增强数据转换失败: {e}")
            # 回退到基础转换
            return self.data_exchange.convert_aero_to_acoustic(aero_data)

    def _interpolate_pressure_distribution(self, pressure: np.ndarray, positions: np.ndarray) -> np.ndarray:
        """插值压力分布到声学网格"""
        if self.interpolation_method == 'rbf':
            return self._rbf_interpolation(pressure, positions)
        elif self.interpolation_method == 'linear':
            return self._linear_interpolation(pressure, positions)
        elif self.interpolation_method == 'cubic':
            return self._cubic_interpolation(pressure, positions)
        else:
            return pressure  # 无插值

    def _rbf_interpolation(self, pressure: np.ndarray, positions: np.ndarray) -> np.ndarray:
        """径向基函数插值"""
        try:
            from scipy.interpolate import RBFInterpolator

            # 简化的RBF插值实现
            if len(pressure.shape) == 1:
                # 1D压力数据
                interpolator = RBFInterpolator(positions, pressure, kernel='thin_plate_spline')
                # 在更密集的网格上插值
                dense_positions = self._generate_dense_grid(positions)
                interpolated_pressure = interpolator(dense_positions)
                return interpolated_pressure
            else:
                return pressure  # 多维数据暂不处理

        except ImportError:
            print("   ⚠️ SciPy不可用，回退到线性插值")
            return self._linear_interpolation(pressure, positions)
        except Exception as e:
            print(f"   ⚠️ RBF插值失败: {e}")
            return pressure

    def _linear_interpolation(self, pressure: np.ndarray, positions: np.ndarray) -> np.ndarray:
        """线性插值"""
        # 简化的线性插值实现
        return pressure  # 暂时返回原始数据

    def _cubic_interpolation(self, pressure: np.ndarray, positions: np.ndarray) -> np.ndarray:
        """三次样条插值"""
        # 简化的三次插值实现
        return pressure  # 暂时返回原始数据

    def _apply_conservative_mapping(self, forces: np.ndarray, positions: np.ndarray) -> np.ndarray:
        """应用保守量映射"""
        # 确保力的积分守恒
        total_force = np.sum(forces, axis=0)

        # 重新分布力以保持守恒
        n_points = len(forces)
        if n_points > 0:
            average_force = total_force / n_points
            conservative_forces = forces + (average_force - np.mean(forces, axis=0))
        else:
            conservative_forces = forces

        return conservative_forces

    def _compute_time_derivatives(self, current_aero_data: AerodynamicData) -> Dict[str, np.ndarray]:
        """计算时间导数"""
        if len(self.aero_results_history) == 0:
            return {}

        prev_aero_data = self.aero_results_history[-1]
        dt = current_aero_data.time_stamp - prev_aero_data.time_stamp

        if dt <= 1e-12:
            return {}

        # 计算各种时间导数
        derivatives = {}

        # 力的时间导数
        force_derivative = (current_aero_data.forces - prev_aero_data.forces) / dt
        derivatives['force_derivative'] = force_derivative

        # 位置的时间导数（速度）
        position_derivative = (current_aero_data.blade_positions - prev_aero_data.blade_positions) / dt
        derivatives['velocity'] = position_derivative

        return derivatives

    def _generate_dense_grid(self, positions: np.ndarray) -> np.ndarray:
        """生成更密集的网格点"""
        # 简化实现：在现有点之间插入中点
        if len(positions) < 2:
            return positions

        dense_positions = [positions[0]]
        for i in range(len(positions) - 1):
            # 添加原始点
            dense_positions.append(positions[i + 1])
            # 添加中点
            midpoint = (positions[i] + positions[i + 1]) / 2
            dense_positions.append(midpoint)

        return np.array(dense_positions)

    def _create_enhanced_acoustic_bc(self, aero_data: AerodynamicData, acoustic_input: Dict[str, Any]) -> Dict[str, Any]:
        """创建增强的声学边界条件"""
        acoustic_bc = {
            'aerodynamic_data': aero_data,
            'acoustic_sources': acoustic_input,
            'enhanced_features': {
                'pressure_interpolation': self.enable_pressure_interpolation,
                'conservative_mapping': self.enable_conservative_mapping,
                'interpolation_method': self.interpolation_method
            }
        }

        # 添加时间历史信息
        if len(self.aero_results_history) > 0:
            acoustic_bc['time_history'] = {
                'previous_data': self.aero_results_history[-1],
                'time_step': aero_data.time_stamp - self.aero_results_history[-1].time_stamp
            }

        return acoustic_bc
