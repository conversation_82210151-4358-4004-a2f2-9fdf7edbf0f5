"""
模型工厂
=======

提供统一的物理模型创建接口

基于adevice_complement4.md技术规范实现
"""

from typing import Dict, Any, Optional, Type
import warnings


class ModelFactory:
    """
    模型工厂（基于adevice_complement4.md规范）
    
    提供统一的物理模型创建和管理接口
    """
    
    def __init__(self):
        """初始化模型工厂"""
        self._vortex_models = {}
        self._airfoil_models = {}
        self._noise_models = {}
        self._correction_models = {}
        
        # 注册默认模型
        self._register_default_models()
    
    def _register_default_models(self):
        """注册默认模型"""
        try:
            # 涡核模型
            self._vortex_models.update({
                'rankine': 'core.physics.vortex_models.RankineVortexCore',
                'lamb_oseen': 'core.physics.vortex_models.LambOseenVortexCore',
                'vatistas': 'core.physics.vortex_models.VatistasVortexCore',
                'scally': 'core.physics.vortex_models.ScallyVortexCore',
                'kaufmann': 'core.physics.vortex_models.KaufmannVortexCore',
                'compressible': 'core.physics.vortex_models.CompressibleVortexCore',
                'time_evolving': 'core.physics.vortex_models.TimeEvolvingVortexCore'
            })
            
            # 翼型模型
            self._airfoil_models.update({
                'static': 'core.aerodynamics.airfoil_models.StaticAirfoilModel',
                'dynamic_stall': 'core.aerodynamics.airfoil_models.DynamicStallModel',
                'leishman_beddoes': 'core.aerodynamics.airfoil_models.LeishmanBeddoesModel'
            })
            
            # 噪声模型
            self._noise_models.update({
                'bpm': 'core.acoustics.bpm_noise_model.BPMNoiseModel',
                'broadband': 'core.acoustics.broadband_noise.BroadbandNoiseModel',
                'tonal': 'core.acoustics.tonal_noise.TonalNoiseModel'
            })
            
            # 修正模型
            self._correction_models.update({
                'compressibility': 'core.physics.corrections.CompressibilityCorrection',
                'tip_loss': 'core.physics.corrections.TipLossCorrection',
                'hub_loss': 'core.physics.corrections.HubLossCorrection',
                'transonic': 'core.physics.corrections.TransonicCorrection'
            })
            
        except Exception as e:
            warnings.warn(f"默认模型注册失败: {e}")
    
    def create_vortex_model(self, model_type: str, **kwargs) -> Any:
        """
        创建涡核模型
        
        Args:
            model_type: 模型类型
            **kwargs: 模型参数
            
        Returns:
            model: 涡核模型实例
        """
        try:
            if model_type not in self._vortex_models:
                raise ValueError(f"不支持的涡核模型类型: {model_type}")
            
            model_class_path = self._vortex_models[model_type]
            model_class = self._import_class(model_class_path)
            
            # 创建模型实例
            model = model_class(**kwargs)
            
            print(f"✅ 创建涡核模型: {model_type}")
            return model
            
        except Exception as e:
            print(f"   ❌ 涡核模型创建失败: {e}")
            raise
    
    def create_airfoil_model(self, model_type: str, config: Dict[str, Any]) -> Any:
        """
        创建翼型模型
        
        Args:
            model_type: 模型类型
            config: 模型配置
            
        Returns:
            model: 翼型模型实例
        """
        try:
            if model_type not in self._airfoil_models:
                raise ValueError(f"不支持的翼型模型类型: {model_type}")
            
            model_class_path = self._airfoil_models[model_type]
            model_class = self._import_class(model_class_path)
            
            # 创建模型实例
            model = model_class(config)
            
            print(f"✅ 创建翼型模型: {model_type}")
            return model
            
        except Exception as e:
            print(f"   ❌ 翼型模型创建失败: {e}")
            raise
    
    def create_noise_model(self, model_type: str, config: Dict[str, Any]) -> Any:
        """
        创建噪声模型
        
        Args:
            model_type: 模型类型
            config: 模型配置
            
        Returns:
            model: 噪声模型实例
        """
        try:
            if model_type not in self._noise_models:
                raise ValueError(f"不支持的噪声模型类型: {model_type}")
            
            model_class_path = self._noise_models[model_type]
            model_class = self._import_class(model_class_path)
            
            # 创建模型实例
            model = model_class(config)
            
            print(f"✅ 创建噪声模型: {model_type}")
            return model
            
        except Exception as e:
            print(f"   ❌ 噪声模型创建失败: {e}")
            raise
    
    def create_correction_model(self, model_type: str, config: Dict[str, Any]) -> Any:
        """
        创建修正模型
        
        Args:
            model_type: 模型类型
            config: 模型配置
            
        Returns:
            model: 修正模型实例
        """
        try:
            if model_type not in self._correction_models:
                raise ValueError(f"不支持的修正模型类型: {model_type}")
            
            model_class_path = self._correction_models[model_type]
            model_class = self._import_class(model_class_path)
            
            # 创建模型实例
            model = model_class(config)
            
            print(f"✅ 创建修正模型: {model_type}")
            return model
            
        except Exception as e:
            print(f"   ❌ 修正模型创建失败: {e}")
            raise
    
    def register_model(self, category: str, model_type: str, class_path: str):
        """
        注册新的模型
        
        Args:
            category: 模型类别 ('vortex', 'airfoil', 'noise', 'correction')
            model_type: 模型类型
            class_path: 类路径
        """
        try:
            if category == 'vortex':
                self._vortex_models[model_type] = class_path
            elif category == 'airfoil':
                self._airfoil_models[model_type] = class_path
            elif category == 'noise':
                self._noise_models[model_type] = class_path
            elif category == 'correction':
                self._correction_models[model_type] = class_path
            else:
                raise ValueError(f"不支持的模型类别: {category}")
            
            print(f"✅ 注册模型: {category}.{model_type}")
            
        except Exception as e:
            print(f"   ❌ 模型注册失败: {e}")
    
    def list_available_models(self) -> Dict[str, list]:
        """
        列出可用的模型
        
        Returns:
            models: 可用模型字典
        """
        return {
            'vortex': list(self._vortex_models.keys()),
            'airfoil': list(self._airfoil_models.keys()),
            'noise': list(self._noise_models.keys()),
            'correction': list(self._correction_models.keys())
        }
    
    def _import_class(self, class_path: str) -> Type:
        """
        动态导入类
        
        Args:
            class_path: 类路径
            
        Returns:
            class_type: 类类型
        """
        try:
            module_path, class_name = class_path.rsplit('.', 1)
            
            # 动态导入模块
            import importlib
            module = importlib.import_module(module_path)
            
            # 获取类
            class_type = getattr(module, class_name)
            
            return class_type
            
        except Exception as e:
            raise ImportError(f"无法导入类 {class_path}: {e}")
    
    def create_model_suite(self, suite_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建模型套件
        
        Args:
            suite_config: 套件配置
            
        Returns:
            model_suite: 模型套件
        """
        try:
            model_suite = {}
            
            # 创建涡核模型
            if 'vortex' in suite_config:
                vortex_config = suite_config['vortex']
                model_type = vortex_config.get('type', 'vatistas')
                model_params = vortex_config.get('params', {})
                model_suite['vortex'] = self.create_vortex_model(model_type, **model_params)
            
            # 创建翼型模型
            if 'airfoil' in suite_config:
                airfoil_config = suite_config['airfoil']
                model_type = airfoil_config.get('type', 'static')
                model_suite['airfoil'] = self.create_airfoil_model(
                    model_type, airfoil_config.get('config', {})
                )
            
            # 创建噪声模型
            if 'noise' in suite_config:
                noise_config = suite_config['noise']
                model_type = noise_config.get('type', 'bpm')
                model_suite['noise'] = self.create_noise_model(
                    model_type, noise_config.get('config', {})
                )
            
            # 创建修正模型
            if 'correction' in suite_config:
                correction_config = suite_config['correction']
                model_type = correction_config.get('type', 'compressibility')
                model_suite['correction'] = self.create_correction_model(
                    model_type, correction_config.get('config', {})
                )
            
            print(f"✅ 创建模型套件，包含 {len(model_suite)} 个模型")
            return model_suite
            
        except Exception as e:
            print(f"   ❌ 模型套件创建失败: {e}")
            raise


# 全局模型工厂实例
_global_model_factory = None

def get_model_factory() -> ModelFactory:
    """获取全局模型工厂实例"""
    global _global_model_factory
    if _global_model_factory is None:
        _global_model_factory = ModelFactory()
    return _global_model_factory

def create_model(category: str, model_type: str, config: Dict[str, Any] = None, **kwargs) -> Any:
    """
    便捷的模型创建函数
    
    Args:
        category: 模型类别
        model_type: 模型类型
        config: 模型配置
        **kwargs: 额外参数
        
    Returns:
        model: 模型实例
    """
    factory = get_model_factory()
    
    if category == 'vortex':
        return factory.create_vortex_model(model_type, **kwargs)
    elif category == 'airfoil':
        return factory.create_airfoil_model(model_type, config or {})
    elif category == 'noise':
        return factory.create_noise_model(model_type, config or {})
    elif category == 'correction':
        return factory.create_correction_model(model_type, config or {})
    else:
        raise ValueError(f"不支持的模型类别: {category}")
