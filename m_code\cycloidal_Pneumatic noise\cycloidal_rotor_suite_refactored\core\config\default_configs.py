"""
默认配置
=======

提供系统默认配置定义

基于adevice_complement4.md技术规范实现
"""

from typing import Dict, Any


class DefaultConfigs:
    """
    默认配置管理器（基于adevice_complement4.md规范）
    
    提供所有模块的默认配置
    """
    
    def __init__(self):
        """初始化默认配置"""
        self._simulation_config = self._create_simulation_config()
        self._geometry_config = self._create_geometry_config()
        self._physics_config = self._create_physics_config()
        self._solver_config = self._create_solver_config()
        self._acoustic_config = self._create_acoustic_config()
        self._coupling_config = self._create_coupling_config()
    
    def _create_simulation_config(self) -> Dict[str, Any]:
        """创建仿真配置"""
        return {
            'name': 'cycloidal_rotor_simulation',
            'description': 'Cycloidal rotor aeroacoustic simulation',
            'version': '1.0.0',
            'time_settings': {
                'start_time': 0.0,
                'end_time': 1.0,
                'time_step': 0.001,
                'adaptive_time_stepping': True,
                'max_time_step': 0.01,
                'min_time_step': 1e-6
            },
            'convergence': {
                'tolerance': 1e-6,
                'max_iterations': 1000,
                'residual_type': 'relative'
            },
            'output': {
                'output_directory': './results',
                'save_frequency': 10,
                'output_format': 'hdf5',
                'compression': True
            },
            'parallel': {
                'use_mpi': False,
                'num_processes': 1,
                'use_gpu': True,
                'gpu_device_id': 0
            }
        }
    
    def _create_geometry_config(self) -> Dict[str, Any]:
        """创建几何配置"""
        return {
            'rotor': {
                'radius': 1.0,
                'blade_count': 4,
                'hub_radius': 0.1,
                'blade_chord': 0.1,
                'blade_twist': 0.0,
                'blade_pitch': 0.0
            },
            'airfoil': {
                'type': 'NACA0012',
                'thickness': 0.12,
                'camber': 0.0,
                'camber_position': 0.4
            },
            'mesh': {
                'type': 'structured',
                'chordwise_panels': 20,
                'spanwise_panels': 15,
                'wake_panels': 100,
                'adaptive_refinement': True,
                'refinement_threshold': 0.01
            },
            'domain': {
                'x_extent': [-5.0, 5.0],
                'y_extent': [-5.0, 5.0],
                'z_extent': [-2.0, 2.0],
                'boundary_conditions': {
                    'far_field': 'free_stream',
                    'rotor_surface': 'no_slip',
                    'symmetry': 'slip'
                }
            }
        }
    
    def _create_physics_config(self) -> Dict[str, Any]:
        """创建物理配置"""
        return {
            'fluid_properties': {
                'density': 1.225,
                'viscosity': 1.5e-5,
                'speed_of_sound': 343.0,
                'temperature': 288.15,
                'pressure': 101325.0
            },
            'flow_conditions': {
                'free_stream_velocity': [10.0, 0.0, 0.0],
                'angle_of_attack': 0.0,
                'sideslip_angle': 0.0,
                'mach_number': 0.029,
                'reynolds_number': 6.7e4
            },
            'rotor_kinematics': {
                'angular_velocity': 100.0,
                'advance_ratio': 0.2,
                'collective_pitch': 0.0,
                'cyclic_pitch': [0.0, 0.0]
            },
            'corrections': {
                'compressibility': True,
                'tip_loss': True,
                'hub_loss': True,
                'dynamic_stall': True,
                'three_dimensional_effects': True
            },
            'vortex_model': {
                'type': 'vatistas',
                'core_radius': 0.01,
                'n_parameter': 2.0,
                'time_evolution': True,
                'viscous_diffusion': True
            }
        }
    
    def _create_solver_config(self) -> Dict[str, Any]:
        """创建求解器配置"""
        return {
            'aerodynamic': {
                'primary_solver': 'bemt',
                'secondary_solver': 'uvlm',
                'bemt': {
                    'tolerance': 1e-6,
                    'max_iterations': 100,
                    'relaxation_factor': 0.7,
                    'dynamic_stall_model': 'leishman_beddoes'
                },
                'uvlm': {
                    'wake_length': 10.0,
                    'wake_rollup': True,
                    'free_wake': True,
                    'gpu_acceleration': True,
                    'numerical_stabilization': True
                },
                'panel': {
                    'panel_type': 'quadrilateral',
                    'singularity_type': 'doublet',
                    'wake_model': 'prescribed'
                }
            },
            'linear': {
                'solver_type': 'adaptive',
                'direct_solver': 'lu',
                'iterative_solver': 'gmres',
                'preconditioner': 'ilu',
                'tolerance': 1e-8,
                'max_iterations': 1000,
                'use_gpu': True
            },
            'nonlinear': {
                'method': 'newton_raphson',
                'tolerance': 1e-6,
                'max_iterations': 50,
                'line_search': True,
                'jacobian_update': 'broyden'
            }
        }
    
    def _create_acoustic_config(self) -> Dict[str, Any]:
        """创建声学配置"""
        return {
            'fwh': {
                'observer_positions': [
                    [10.0, 0.0, 0.0],
                    [0.0, 10.0, 0.0],
                    [0.0, 0.0, 10.0]
                ],
                'time_history_length': 1.0,
                'sampling_frequency': 10000.0,
                'retarded_time': True,
                'doppler_effect': True,
                'convective_amplification': True
            },
            'bpm': {
                'frequency_range': [10.0, 10000.0],
                'frequency_resolution': 100,
                'noise_mechanisms': [
                    'turbulent_boundary_layer',
                    'separation_stall',
                    'tip_vortex',
                    'trailing_edge',
                    'leading_edge'
                ],
                'directivity_calculation': True,
                'atmospheric_absorption': True
            },
            'broadband': {
                'turbulence_intensity': 0.05,
                'integral_length_scale': 0.1,
                'spectral_model': 'von_karman',
                'anisotropy_factor': 1.0
            },
            'propagation': {
                'atmospheric_model': 'standard',
                'ground_reflection': False,
                'atmospheric_absorption': True,
                'scattering': False
            }
        }
    
    def _create_coupling_config(self) -> Dict[str, Any]:
        """创建耦合配置"""
        return {
            'aero_acoustic': {
                'coupling_method': 'one_way',
                'data_transfer_method': 'interpolation',
                'time_synchronization': True,
                'spatial_interpolation': 'linear',
                'temporal_interpolation': 'linear'
            },
            'fluid_structure': {
                'coupling_method': 'two_way',
                'structural_damping': 0.02,
                'modal_analysis': True,
                'geometric_nonlinearity': False
            },
            'convergence': {
                'tolerance': 1e-4,
                'max_iterations': 10,
                'relaxation_factor': 0.7,
                'acceleration_method': 'aitken'
            },
            'load_balancing': {
                'enabled': True,
                'method': 'dynamic',
                'rebalance_frequency': 100
            }
        }
    
    def get_simulation_config(self) -> Dict[str, Any]:
        """获取仿真配置"""
        return self._simulation_config.copy()
    
    def get_geometry_config(self) -> Dict[str, Any]:
        """获取几何配置"""
        return self._geometry_config.copy()
    
    def get_physics_config(self) -> Dict[str, Any]:
        """获取物理配置"""
        return self._physics_config.copy()
    
    def get_solver_config(self) -> Dict[str, Any]:
        """获取求解器配置"""
        return self._solver_config.copy()
    
    def get_acoustic_config(self) -> Dict[str, Any]:
        """获取声学配置"""
        return self._acoustic_config.copy()
    
    def get_coupling_config(self) -> Dict[str, Any]:
        """获取耦合配置"""
        return self._coupling_config.copy()
    
    def get_all_defaults(self) -> Dict[str, Dict[str, Any]]:
        """获取所有默认配置"""
        return {
            'simulation': self.get_simulation_config(),
            'geometry': self.get_geometry_config(),
            'physics': self.get_physics_config(),
            'solver': self.get_solver_config(),
            'acoustic': self.get_acoustic_config(),
            'coupling': self.get_coupling_config()
        }
    
    def create_complete_config(self) -> Dict[str, Any]:
        """创建完整的配置"""
        return {
            'simulation': self.get_simulation_config(),
            'geometry': self.get_geometry_config(),
            'physics': self.get_physics_config(),
            'solver': self.get_solver_config(),
            'acoustic': self.get_acoustic_config(),
            'coupling': self.get_coupling_config()
        }
