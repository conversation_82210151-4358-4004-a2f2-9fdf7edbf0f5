"""
参数验证器
=========

提供配置参数的验证功能

基于adevice_complement4.md技术规范实现
"""

from typing import Dict, Any, List, Union, Optional, Callable
import numpy as np
import warnings


class ParameterValidator:
    """
    参数验证器（基于adevice_complement4.md规范）
    
    提供配置参数的类型检查、范围验证和逻辑验证
    """
    
    def __init__(self):
        """初始化参数验证器"""
        self.validation_rules = {}
        self.custom_validators = {}
        
        # 注册默认验证规则
        self._register_default_rules()
    
    def _register_default_rules(self):
        """注册默认验证规则"""
        # 仿真参数验证规则
        self.validation_rules['simulation'] = {
            'time_settings.start_time': {'type': (int, float), 'min': 0.0},
            'time_settings.end_time': {'type': (int, float), 'min': 0.0},
            'time_settings.time_step': {'type': (int, float), 'min': 1e-8, 'max': 1.0},
            'convergence.tolerance': {'type': (int, float), 'min': 1e-12, 'max': 1e-2},
            'convergence.max_iterations': {'type': int, 'min': 1, 'max': 10000},
            'parallel.num_processes': {'type': int, 'min': 1, 'max': 1000},
            'parallel.gpu_device_id': {'type': int, 'min': 0, 'max': 16}
        }
        
        # 几何参数验证规则
        self.validation_rules['geometry'] = {
            'rotor.radius': {'type': (int, float), 'min': 0.1, 'max': 100.0},
            'rotor.blade_count': {'type': int, 'min': 2, 'max': 20},
            'rotor.hub_radius': {'type': (int, float), 'min': 0.01, 'max': 10.0},
            'rotor.blade_chord': {'type': (int, float), 'min': 0.01, 'max': 10.0},
            'airfoil.thickness': {'type': (int, float), 'min': 0.01, 'max': 0.5},
            'mesh.chordwise_panels': {'type': int, 'min': 5, 'max': 200},
            'mesh.spanwise_panels': {'type': int, 'min': 3, 'max': 100},
            'mesh.wake_panels': {'type': int, 'min': 10, 'max': 1000}
        }
        
        # 物理参数验证规则
        self.validation_rules['physics'] = {
            'fluid_properties.density': {'type': (int, float), 'min': 0.1, 'max': 10.0},
            'fluid_properties.viscosity': {'type': (int, float), 'min': 1e-6, 'max': 1e-3},
            'fluid_properties.speed_of_sound': {'type': (int, float), 'min': 100.0, 'max': 500.0},
            'flow_conditions.mach_number': {'type': (int, float), 'min': 0.0, 'max': 2.0},
            'flow_conditions.reynolds_number': {'type': (int, float), 'min': 1e3, 'max': 1e8},
            'rotor_kinematics.angular_velocity': {'type': (int, float), 'min': 1.0, 'max': 1000.0},
            'rotor_kinematics.advance_ratio': {'type': (int, float), 'min': 0.0, 'max': 2.0},
            'vortex_model.core_radius': {'type': (int, float), 'min': 1e-6, 'max': 1.0},
            'vortex_model.n_parameter': {'type': (int, float), 'min': 0.5, 'max': 10.0}
        }
        
        # 求解器参数验证规则
        self.validation_rules['solver'] = {
            'aerodynamic.bemt.tolerance': {'type': (int, float), 'min': 1e-12, 'max': 1e-2},
            'aerodynamic.bemt.max_iterations': {'type': int, 'min': 10, 'max': 1000},
            'aerodynamic.bemt.relaxation_factor': {'type': (int, float), 'min': 0.1, 'max': 1.0},
            'aerodynamic.uvlm.wake_length': {'type': (int, float), 'min': 1.0, 'max': 100.0},
            'linear.tolerance': {'type': (int, float), 'min': 1e-15, 'max': 1e-3},
            'linear.max_iterations': {'type': int, 'min': 10, 'max': 10000},
            'nonlinear.tolerance': {'type': (int, float), 'min': 1e-12, 'max': 1e-3},
            'nonlinear.max_iterations': {'type': int, 'min': 5, 'max': 200}
        }
        
        # 声学参数验证规则
        self.validation_rules['acoustic'] = {
            'fwh.sampling_frequency': {'type': (int, float), 'min': 1000.0, 'max': 100000.0},
            'fwh.time_history_length': {'type': (int, float), 'min': 0.1, 'max': 10.0},
            'bpm.frequency_range': {'type': list, 'length': 2},
            'bpm.frequency_resolution': {'type': int, 'min': 10, 'max': 10000},
            'broadband.turbulence_intensity': {'type': (int, float), 'min': 0.001, 'max': 0.5},
            'broadband.integral_length_scale': {'type': (int, float), 'min': 0.001, 'max': 10.0}
        }
        
        # 耦合参数验证规则
        self.validation_rules['coupling'] = {
            'convergence.tolerance': {'type': (int, float), 'min': 1e-8, 'max': 1e-2},
            'convergence.max_iterations': {'type': int, 'min': 2, 'max': 100},
            'convergence.relaxation_factor': {'type': (int, float), 'min': 0.1, 'max': 1.0},
            'load_balancing.rebalance_frequency': {'type': int, 'min': 1, 'max': 10000}
        }
    
    def validate_config(self, config_category: str, config: Dict[str, Any]) -> List[str]:
        """
        验证配置
        
        Args:
            config_category: 配置类别
            config: 配置字典
            
        Returns:
            errors: 验证错误列表
        """
        errors = []
        
        try:
            if config_category in self.validation_rules:
                rules = self.validation_rules[config_category]
                errors.extend(self._validate_against_rules(config, rules))
            
            # 应用自定义验证器
            if config_category in self.custom_validators:
                validator = self.custom_validators[config_category]
                custom_errors = validator(config)
                if custom_errors:
                    errors.extend(custom_errors)
            
            # 逻辑一致性验证
            logic_errors = self._validate_logic_consistency(config_category, config)
            errors.extend(logic_errors)
            
        except Exception as e:
            errors.append(f"验证过程中发生错误: {e}")
        
        return errors
    
    def _validate_against_rules(self, config: Dict[str, Any], 
                               rules: Dict[str, Dict[str, Any]]) -> List[str]:
        """根据规则验证配置"""
        errors = []
        
        for param_path, rule in rules.items():
            try:
                value = self._get_nested_value(config, param_path)
                
                if value is None:
                    if rule.get('required', False):
                        errors.append(f"缺少必需参数: {param_path}")
                    continue
                
                # 类型检查
                if 'type' in rule:
                    expected_type = rule['type']
                    if not isinstance(value, expected_type):
                        errors.append(f"参数 {param_path} 类型错误: 期望 {expected_type}, 实际 {type(value)}")
                        continue
                
                # 范围检查
                if isinstance(value, (int, float)):
                    if 'min' in rule and value < rule['min']:
                        errors.append(f"参数 {param_path} 值过小: {value} < {rule['min']}")
                    
                    if 'max' in rule and value > rule['max']:
                        errors.append(f"参数 {param_path} 值过大: {value} > {rule['max']}")
                
                # 长度检查
                if isinstance(value, (list, tuple, str)):
                    if 'length' in rule and len(value) != rule['length']:
                        errors.append(f"参数 {param_path} 长度错误: 期望 {rule['length']}, 实际 {len(value)}")
                    
                    if 'min_length' in rule and len(value) < rule['min_length']:
                        errors.append(f"参数 {param_path} 长度过短: {len(value)} < {rule['min_length']}")
                    
                    if 'max_length' in rule and len(value) > rule['max_length']:
                        errors.append(f"参数 {param_path} 长度过长: {len(value)} > {rule['max_length']}")
                
                # 选项检查
                if 'choices' in rule and value not in rule['choices']:
                    errors.append(f"参数 {param_path} 值无效: {value} 不在 {rule['choices']} 中")
                
            except Exception as e:
                errors.append(f"验证参数 {param_path} 时发生错误: {e}")
        
        return errors
    
    def _get_nested_value(self, config: Dict[str, Any], param_path: str) -> Any:
        """获取嵌套参数值"""
        keys = param_path.split('.')
        value = config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value
    
    def _validate_logic_consistency(self, config_category: str, 
                                  config: Dict[str, Any]) -> List[str]:
        """验证逻辑一致性"""
        errors = []
        
        try:
            if config_category == 'simulation':
                errors.extend(self._validate_simulation_logic(config))
            elif config_category == 'geometry':
                errors.extend(self._validate_geometry_logic(config))
            elif config_category == 'physics':
                errors.extend(self._validate_physics_logic(config))
            elif config_category == 'solver':
                errors.extend(self._validate_solver_logic(config))
            elif config_category == 'acoustic':
                errors.extend(self._validate_acoustic_logic(config))
            elif config_category == 'coupling':
                errors.extend(self._validate_coupling_logic(config))
                
        except Exception as e:
            errors.append(f"逻辑一致性验证失败: {e}")
        
        return errors
    
    def _validate_simulation_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证仿真逻辑"""
        errors = []
        
        # 时间设置逻辑
        time_settings = config.get('time_settings', {})
        start_time = time_settings.get('start_time', 0.0)
        end_time = time_settings.get('end_time', 1.0)
        time_step = time_settings.get('time_step', 0.001)
        
        if end_time <= start_time:
            errors.append("结束时间必须大于开始时间")
        
        if time_step >= (end_time - start_time):
            errors.append("时间步长过大，应小于总仿真时间")
        
        # 自适应时间步长逻辑
        if time_settings.get('adaptive_time_stepping', False):
            min_dt = time_settings.get('min_time_step', 1e-6)
            max_dt = time_settings.get('max_time_step', 0.01)
            
            if min_dt >= max_dt:
                errors.append("最小时间步长必须小于最大时间步长")
            
            if time_step < min_dt or time_step > max_dt:
                errors.append("初始时间步长应在最小和最大时间步长之间")
        
        return errors
    
    def _validate_geometry_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证几何逻辑"""
        errors = []
        
        rotor = config.get('rotor', {})
        radius = rotor.get('radius', 1.0)
        hub_radius = rotor.get('hub_radius', 0.1)
        chord = rotor.get('blade_chord', 0.1)
        
        if hub_radius >= radius:
            errors.append("轮毂半径必须小于转子半径")
        
        if chord > radius:
            errors.append("桨叶弦长不应大于转子半径")
        
        # 网格逻辑
        mesh = config.get('mesh', {})
        chordwise = mesh.get('chordwise_panels', 20)
        spanwise = mesh.get('spanwise_panels', 15)
        
        if chordwise * spanwise > 10000:
            warnings.warn("网格面板数量过多，可能影响计算效率")
        
        return errors
    
    def _validate_physics_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证物理逻辑"""
        errors = []
        
        # 流动条件逻辑
        flow = config.get('flow_conditions', {})
        mach = flow.get('mach_number', 0.1)
        reynolds = flow.get('reynolds_number', 1e5)
        
        if mach > 0.8:
            warnings.warn("高马赫数流动，建议启用压缩性修正")
        
        if reynolds < 1e4:
            warnings.warn("低雷诺数流动，层流效应可能显著")
        
        # 转子运动学逻辑
        kinematics = config.get('rotor_kinematics', {})
        omega = kinematics.get('angular_velocity', 100.0)
        advance_ratio = kinematics.get('advance_ratio', 0.2)
        
        if advance_ratio > 1.0:
            warnings.warn("前进比过大，可能导致桨叶失速")
        
        return errors
    
    def _validate_solver_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证求解器逻辑"""
        errors = []
        
        # 气动求解器逻辑
        aero = config.get('aerodynamic', {})
        primary = aero.get('primary_solver', 'bemt')
        secondary = aero.get('secondary_solver', 'uvlm')
        
        if primary == secondary:
            errors.append("主求解器和辅助求解器不能相同")
        
        # 线性求解器逻辑
        linear = config.get('linear', {})
        solver_type = linear.get('solver_type', 'direct')
        tolerance = linear.get('tolerance', 1e-8)
        max_iter = linear.get('max_iterations', 1000)
        
        if solver_type == 'iterative' and tolerance > 1e-6:
            warnings.warn("迭代求解器容差较大，可能影响精度")
        
        return errors
    
    def _validate_acoustic_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证声学逻辑"""
        errors = []
        
        # FW-H求解器逻辑
        fwh = config.get('fwh', {})
        sampling_freq = fwh.get('sampling_frequency', 10000.0)
        time_length = fwh.get('time_history_length', 1.0)
        
        if sampling_freq * time_length < 1000:
            warnings.warn("采样点数过少，可能影响频谱分辨率")
        
        # BPM模型逻辑
        bpm = config.get('bpm', {})
        freq_range = bmp.get('frequency_range', [10.0, 10000.0])
        
        if len(freq_range) == 2 and freq_range[1] <= freq_range[0]:
            errors.append("频率范围上限必须大于下限")
        
        return errors
    
    def _validate_coupling_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证耦合逻辑"""
        errors = []
        
        convergence = config.get('convergence', {})
        tolerance = convergence.get('tolerance', 1e-4)
        max_iter = convergence.get('max_iterations', 10)
        
        if tolerance < 1e-8 and max_iter < 20:
            warnings.warn("严格的收敛容差可能需要更多迭代次数")
        
        return errors
    
    def register_custom_validator(self, config_category: str, 
                                validator: Callable[[Dict[str, Any]], List[str]]):
        """
        注册自定义验证器
        
        Args:
            config_category: 配置类别
            validator: 验证函数
        """
        self.custom_validators[config_category] = validator
        print(f"   ✅ 注册自定义验证器: {config_category}")
    
    def add_validation_rule(self, config_category: str, param_path: str, 
                          rule: Dict[str, Any]):
        """
        添加验证规则
        
        Args:
            config_category: 配置类别
            param_path: 参数路径
            rule: 验证规则
        """
        if config_category not in self.validation_rules:
            self.validation_rules[config_category] = {}
        
        self.validation_rules[config_category][param_path] = rule
        print(f"   ✅ 添加验证规则: {config_category}.{param_path}")


# 全局参数验证器实例
_global_parameter_validator = None

def get_parameter_validator() -> ParameterValidator:
    """获取全局参数验证器实例"""
    global _global_parameter_validator
    if _global_parameter_validator is None:
        _global_parameter_validator = ParameterValidator()
    return _global_parameter_validator

def validate_config(config_category: str, config: Dict[str, Any]) -> List[str]:
    """便捷的配置验证函数"""
    validator = get_parameter_validator()
    return validator.validate_config(config_category, config)
