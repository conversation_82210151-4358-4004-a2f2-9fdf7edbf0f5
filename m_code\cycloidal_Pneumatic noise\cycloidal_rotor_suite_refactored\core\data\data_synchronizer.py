"""
数据同步器
=========

提供多求解器间的数据同步功能

基于adevice_complement4.md技术规范实现
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
import threading
import time
from abc import ABC, abstractmethod
import warnings


class DataSynchronizer:
    """
    数据同步器（基于adevice_complement4.md规范）
    
    提供多求解器间的高效数据同步和传输
    """
    
    def __init__(self, sync_config: Dict[str, Any]):
        """
        初始化数据同步器
        
        Args:
            sync_config: 同步配置
        """
        self.sync_config = sync_config
        self.sync_method = sync_config.get('method', 'direct')
        self.interpolation_method = sync_config.get('interpolation', 'linear')
        self.time_synchronization = sync_config.get('time_sync', True)
        self.spatial_synchronization = sync_config.get('spatial_sync', True)
        
        # 数据缓存
        self.data_cache = {}
        self.time_stamps = {}
        self.spatial_grids = {}
        
        # 同步状态
        self.sync_lock = threading.Lock()
        self.last_sync_time = 0.0
        self.sync_frequency = sync_config.get('sync_frequency', 100.0)  # Hz
        
        # 插值器
        self.interpolators = {}
        
        print(f"✅ 数据同步器初始化完成")
        print(f"   同步方法: {self.sync_method}")
        print(f"   插值方法: {self.interpolation_method}")
        print(f"   时间同步: {'启用' if self.time_synchronization else '禁用'}")
        print(f"   空间同步: {'启用' if self.spatial_synchronization else '禁用'}")
    
    def register_data_source(self, source_name: str, data_info: Dict[str, Any]):
        """
        注册数据源
        
        Args:
            source_name: 数据源名称
            data_info: 数据信息
        """
        try:
            self.data_cache[source_name] = {
                'data': None,
                'timestamp': 0.0,
                'grid': data_info.get('grid', None),
                'data_type': data_info.get('type', 'field'),
                'dimensions': data_info.get('dimensions', 3),
                'units': data_info.get('units', 'SI')
            }
            
            # 存储空间网格信息
            if 'grid' in data_info:
                self.spatial_grids[source_name] = data_info['grid']
            
            print(f"   ✅ 注册数据源: {source_name}")
            
        except Exception as e:
            print(f"   ❌ 数据源注册失败: {e}")
    
    def update_data(self, source_name: str, data: np.ndarray, timestamp: float):
        """
        更新数据
        
        Args:
            source_name: 数据源名称
            data: 数据数组
            timestamp: 时间戳
        """
        try:
            with self.sync_lock:
                if source_name in self.data_cache:
                    self.data_cache[source_name]['data'] = data.copy()
                    self.data_cache[source_name]['timestamp'] = timestamp
                    self.time_stamps[source_name] = timestamp
                else:
                    warnings.warn(f"未注册的数据源: {source_name}")
                    
        except Exception as e:
            print(f"   ⚠️ 数据更新失败: {e}")
    
    def get_synchronized_data(self, target_name: str, source_names: List[str], 
                            target_grid: Optional[np.ndarray] = None,
                            target_time: Optional[float] = None) -> Dict[str, np.ndarray]:
        """
        获取同步数据
        
        Args:
            target_name: 目标求解器名称
            source_names: 源数据名称列表
            target_grid: 目标网格
            target_time: 目标时间
            
        Returns:
            synchronized_data: 同步后的数据字典
        """
        try:
            with self.sync_lock:
                synchronized_data = {}
                
                for source_name in source_names:
                    if source_name not in self.data_cache:
                        warnings.warn(f"数据源不存在: {source_name}")
                        continue
                    
                    source_data = self.data_cache[source_name]['data']
                    source_time = self.data_cache[source_name]['timestamp']
                    source_grid = self.spatial_grids.get(source_name, None)
                    
                    if source_data is None:
                        warnings.warn(f"数据源 {source_name} 无数据")
                        continue
                    
                    # 时间同步
                    if self.time_synchronization and target_time is not None:
                        time_synced_data = self._time_interpolation(
                            source_data, source_time, target_time
                        )
                    else:
                        time_synced_data = source_data
                    
                    # 空间同步
                    if (self.spatial_synchronization and target_grid is not None 
                        and source_grid is not None):
                        spatial_synced_data = self._spatial_interpolation(
                            time_synced_data, source_grid, target_grid
                        )
                    else:
                        spatial_synced_data = time_synced_data
                    
                    synchronized_data[source_name] = spatial_synced_data
                
                return synchronized_data
                
        except Exception as e:
            print(f"   ❌ 数据同步失败: {e}")
            return {}
    
    def _time_interpolation(self, data: np.ndarray, source_time: float, 
                          target_time: float) -> np.ndarray:
        """时间插值"""
        try:
            # 简化的时间插值实现
            time_diff = abs(target_time - source_time)
            
            if time_diff < 1.0 / self.sync_frequency:
                # 时间差很小，直接使用原数据
                return data
            else:
                # 简单的线性外推（实际应用中应使用历史数据）
                extrapolation_factor = 1.0 + 0.1 * time_diff
                return data * extrapolation_factor
                
        except Exception as e:
            warnings.warn(f"时间插值失败: {e}")
            return data
    
    def _spatial_interpolation(self, data: np.ndarray, source_grid: np.ndarray, 
                             target_grid: np.ndarray) -> np.ndarray:
        """空间插值"""
        try:
            if self.interpolation_method == 'linear':
                return self._linear_interpolation(data, source_grid, target_grid)
            elif self.interpolation_method == 'cubic':
                return self._cubic_interpolation(data, source_grid, target_grid)
            elif self.interpolation_method == 'nearest':
                return self._nearest_interpolation(data, source_grid, target_grid)
            else:
                warnings.warn(f"不支持的插值方法: {self.interpolation_method}")
                return data
                
        except Exception as e:
            warnings.warn(f"空间插值失败: {e}")
            return data
    
    def _linear_interpolation(self, data: np.ndarray, source_grid: np.ndarray, 
                            target_grid: np.ndarray) -> np.ndarray:
        """线性插值"""
        try:
            from scipy.interpolate import griddata
            
            # 重塑数据用于插值
            source_points = source_grid.reshape(-1, source_grid.shape[-1])
            target_points = target_grid.reshape(-1, target_grid.shape[-1])
            
            if data.ndim == 1:
                # 标量场
                interpolated = griddata(source_points, data.flatten(), 
                                      target_points, method='linear', fill_value=0.0)
            else:
                # 向量场
                interpolated = np.zeros((target_points.shape[0], data.shape[-1]))
                for i in range(data.shape[-1]):
                    interpolated[:, i] = griddata(source_points, data[..., i].flatten(),
                                                target_points, method='linear', fill_value=0.0)
            
            # 重塑回目标网格形状
            target_shape = target_grid.shape[:-1] + (data.shape[-1],) if data.ndim > 1 else target_grid.shape[:-1]
            return interpolated.reshape(target_shape)
            
        except ImportError:
            # 如果scipy不可用，使用简化的最近邻插值
            return self._nearest_interpolation(data, source_grid, target_grid)
        except Exception as e:
            warnings.warn(f"线性插值失败: {e}")
            return data
    
    def _cubic_interpolation(self, data: np.ndarray, source_grid: np.ndarray, 
                           target_grid: np.ndarray) -> np.ndarray:
        """三次插值"""
        try:
            from scipy.interpolate import griddata
            
            source_points = source_grid.reshape(-1, source_grid.shape[-1])
            target_points = target_grid.reshape(-1, target_grid.shape[-1])
            
            if data.ndim == 1:
                interpolated = griddata(source_points, data.flatten(), 
                                      target_points, method='cubic', fill_value=0.0)
            else:
                interpolated = np.zeros((target_points.shape[0], data.shape[-1]))
                for i in range(data.shape[-1]):
                    interpolated[:, i] = griddata(source_points, data[..., i].flatten(),
                                                target_points, method='cubic', fill_value=0.0)
            
            target_shape = target_grid.shape[:-1] + (data.shape[-1],) if data.ndim > 1 else target_grid.shape[:-1]
            return interpolated.reshape(target_shape)
            
        except ImportError:
            return self._linear_interpolation(data, source_grid, target_grid)
        except Exception as e:
            warnings.warn(f"三次插值失败: {e}")
            return self._linear_interpolation(data, source_grid, target_grid)
    
    def _nearest_interpolation(self, data: np.ndarray, source_grid: np.ndarray, 
                             target_grid: np.ndarray) -> np.ndarray:
        """最近邻插值"""
        try:
            source_points = source_grid.reshape(-1, source_grid.shape[-1])
            target_points = target_grid.reshape(-1, target_grid.shape[-1])
            
            # 计算距离矩阵
            distances = np.linalg.norm(target_points[:, np.newaxis, :] - 
                                     source_points[np.newaxis, :, :], axis=2)
            
            # 找到最近邻索引
            nearest_indices = np.argmin(distances, axis=1)
            
            # 插值
            if data.ndim == 1:
                interpolated = data.flatten()[nearest_indices]
            else:
                interpolated = data.reshape(-1, data.shape[-1])[nearest_indices]
            
            # 重塑回目标网格形状
            target_shape = target_grid.shape[:-1] + (data.shape[-1],) if data.ndim > 1 else target_grid.shape[:-1]
            return interpolated.reshape(target_shape)
            
        except Exception as e:
            warnings.warn(f"最近邻插值失败: {e}")
            return data
    
    def synchronize_all_data(self, target_time: float) -> Dict[str, Dict[str, np.ndarray]]:
        """
        同步所有数据到指定时间
        
        Args:
            target_time: 目标时间
            
        Returns:
            all_synchronized_data: 所有同步数据
        """
        try:
            all_synchronized_data = {}
            
            with self.sync_lock:
                for source_name, cache_info in self.data_cache.items():
                    if cache_info['data'] is not None:
                        # 时间同步
                        if self.time_synchronization:
                            synced_data = self._time_interpolation(
                                cache_info['data'], cache_info['timestamp'], target_time
                            )
                        else:
                            synced_data = cache_info['data']
                        
                        all_synchronized_data[source_name] = {
                            'data': synced_data,
                            'timestamp': target_time,
                            'original_timestamp': cache_info['timestamp']
                        }
            
            self.last_sync_time = target_time
            return all_synchronized_data
            
        except Exception as e:
            print(f"   ❌ 全数据同步失败: {e}")
            return {}
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """
        获取同步统计信息
        
        Returns:
            statistics: 同步统计
        """
        try:
            with self.sync_lock:
                stats = {
                    'registered_sources': len(self.data_cache),
                    'active_sources': sum(1 for cache in self.data_cache.values() 
                                        if cache['data'] is not None),
                    'last_sync_time': self.last_sync_time,
                    'sync_frequency': self.sync_frequency,
                    'time_synchronization': self.time_synchronization,
                    'spatial_synchronization': self.spatial_synchronization,
                    'interpolation_method': self.interpolation_method
                }
                
                # 数据源详细信息
                source_details = {}
                for name, cache in self.data_cache.items():
                    source_details[name] = {
                        'has_data': cache['data'] is not None,
                        'timestamp': cache['timestamp'],
                        'data_type': cache['data_type'],
                        'dimensions': cache['dimensions']
                    }
                
                stats['source_details'] = source_details
                
                return stats
                
        except Exception as e:
            print(f"   ⚠️ 统计信息获取失败: {e}")
            return {}
    
    def clear_cache(self):
        """清空数据缓存"""
        try:
            with self.sync_lock:
                for cache_info in self.data_cache.values():
                    cache_info['data'] = None
                    cache_info['timestamp'] = 0.0
                
                self.time_stamps.clear()
                print(f"   ✅ 数据缓存已清空")
                
        except Exception as e:
            print(f"   ⚠️ 缓存清空失败: {e}")
    
    def set_sync_frequency(self, frequency: float):
        """
        设置同步频率
        
        Args:
            frequency: 同步频率 [Hz]
        """
        if frequency > 0:
            self.sync_frequency = frequency
            print(f"   ✅ 同步频率设置为: {frequency} Hz")
        else:
            warnings.warn("同步频率必须为正值")


def create_data_synchronizer(sync_config: Dict[str, Any]) -> DataSynchronizer:
    """
    创建数据同步器
    
    Args:
        sync_config: 同步配置
        
    Returns:
        DataSynchronizer: 数据同步器实例
    """
    return DataSynchronizer(sync_config)
